"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Notice } from "@/lib/types"

interface NoticeFormProps {
  initialData?: Notice
  onSubmit: (data: Partial<Notice>) => void
  isLoading: boolean
}

export default function NoticeForm({ initialData, onSubmit, isLoading }: NoticeFormProps) {
  const [formData, setFormData] = useState<Partial<Notice>>(initialData ? {
    ...initialData,
    publish_date: new Date(initialData.publish_date), // Convert to Date
    expiry_date: initialData.expiry_date ? new Date(initialData.expiry_date) : undefined, // Convert to Date or keep as undefined
  } : {
    title: "",
    description: "",
    priority: "medium",
    publish_date: new Date(), // Default to today
    expiry_date: undefined, // Set as undefined
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: name === "publish_date" || name === "expiry_date" ? new Date(value) : value // Convert date strings to Date objects
    }))
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label className="block text-sm font-medium mb-2">Title</label>
        <Input
          name="title"
          value={formData.title}
          onChange={handleChange}
          required
        />
      </div>
      <div>
        <label className="block text-sm font-medium mb-2">Description</label>
        <Textarea
          name="description"
          value={formData.description}
          onChange={handleChange}
          required
        />
      </div>
      <div>
        <label className="block text-sm font-medium mb-2">Priority</label>
        <select
          title="Priority"
          name="priority"
          value={formData.priority}
          onChange={handleChange}
          className="w-full border rounded-md p-2"
          required
        >
          <option value="low">Low</option>
          <option value="medium">Medium</option>
          <option value="high">High</option>
        </select>
      </div>
      <div>
        <label className="block text-sm font-medium mb-2">Publish Date</label>
        <Input
          type="date"
          name="publish_date"
          value={formData.publish_date ? formData.publish_date.toISOString().split("T")[0] : ''}
          onChange={handleChange}
          required
        />
      </div>
      <div>
        <label className="block text-sm font-medium mb-2">Expiry Date</label>
        <Input
          type="date"
          name="expiry_date"
          value={formData.expiry_date ? formData.expiry_date.toISOString().split("T")[0] : ''}
          onChange={handleChange}
        />
      </div>
      <div className="flex justify-end space-x-4">
        <Button type="submit" disabled={isLoading}>
          {isLoading ? "Saving..." : "Save Notice"}
        </Button>
      </div>
    </form>
  )
} 