/* eslint-disable @typescript-eslint/no-explicit-any */
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import pool from './db';

// Types
export interface AdminUser {
  admin_id: number;
  email: string;
  full_name: string;
  is_super_admin: boolean;
}

// JWT Secret - should be in environment variables
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-this-in-production';
const JWT_EXPIRES_IN = '24h';

// Hash password
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 10;
  return bcrypt.hash(password, saltRounds);
}

// Compare password
export async function comparePassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}

// Generate JWT token
export function generateToken(user: AdminUser): string {
  return jwt.sign(
    { 
      id: user.admin_id,
      email: user.email,
      is_super_admin: user.is_super_admin 
    },
    JWT_SECRET,
    { expiresIn: JWT_EXPIRES_IN }
  );
}

// Verify JWT token
export function verifyToken(token: string): any {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch {
    return null;
  }
}

// Set auth cookie
export function setAuthCookie(token: string): void {
  // Create a response object to set the cookie
  const response = NextResponse.next();
  
  // Set the cookie directly on the response
  response.cookies.set({
    name: 'admin_token',
    value: token,
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    maxAge: 60 * 60 * 24, // 1 day
    path: '/',
    sameSite: 'lax', // Changed from 'strict' to 'lax' for better compatibility
  });
  
  // No return since function is void
  // Remove return since function is void
}

// Clear auth cookie
export async function clearAuthCookie(): Promise<void> {
  const cookieStore = await cookies();
  cookieStore.set({
    name: 'admin_token',
    value: '',
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    maxAge: 0,
    path: '/',
    sameSite: 'strict',
  });
}

// Get current admin user from token
export async function getCurrentAdmin(): Promise<AdminUser | null> {
  const cookieStore = cookies();
  const token = (await cookieStore).get('admin_token')?.value;
  
  if (!token) return null;
  
  const decoded = verifyToken(token);
  if (!decoded) return null;
  
  try {
    const [rows] = await pool.query(
      'SELECT admin_id, email, full_name, is_super_admin FROM admin_users WHERE admin_id = ?',
      [decoded.id]
    );
    
    const users = rows as any[];
    if (users.length === 0) return null;
    
    return users[0] as AdminUser;
  } catch (error) {
    console.error('Error fetching current admin:', error);
    return null;
  }
}

// Auth middleware for API routes
export async function authMiddleware(req: NextRequest) {
  const token = req.cookies.get('admin_token')?.value;
  
  if (!token) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  const decoded = verifyToken(token);
  if (!decoded) {
    return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
  }
  
  return null; // No error, proceed
}

// Super admin middleware
export async function superAdminMiddleware(req: NextRequest) {
  const token = req.cookies.get('admin_token')?.value;
  
  if (!token) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  const decoded = verifyToken(token);
  if (!decoded || !decoded.is_super_admin) {
    return NextResponse.json({ error: 'Forbidden: Requires super admin privileges' }, { status: 403 });
  }
  
  return null; // No error, proceed
}

// Add this function if it doesn't exist already
// Fix the verifyAuth function to use admin_token instead of auth_token
export async function verifyAuth(req: NextRequest) {
  try {
    // Get the token from the cookie
    const token = req.cookies.get('admin_token')?.value;
    
    if (!token) {
      return null;
    }
    
    // Verify the token
    const decoded = jwt.verify(token, JWT_SECRET);
    
    // Return the decoded user data
    return decoded;
  } catch (error) {
    console.error('Token verification error:', error);
    return null;
  }
}