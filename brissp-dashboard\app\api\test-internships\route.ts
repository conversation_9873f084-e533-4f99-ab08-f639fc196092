import { NextResponse } from 'next/server';
import pool from '@/lib/db';
import { RowDataPacket } from 'mysql2';

export async function GET() {
  try {
    // Test if the table exists
    const [tableExists] = await pool.query<RowDataPacket[]>(
      `SELECT COUNT(*) as count FROM information_schema.tables 
       WHERE table_schema = DATABASE() AND table_name = 'internship_applications'`
    );

    if (tableExists[0].count === 0) {
      return NextResponse.json({
        error: 'Table internship_applications does not exist',
        tableExists: false
      });
    }

    // Get all records without any filters
    const [allRows] = await pool.query<RowDataPacket[]>(
      'SELECT * FROM internship_applications ORDER BY application_date DESC'
    );

    // Get table structure
    const [tableStructure] = await pool.query<RowDataPacket[]>(
      'DESCRIBE internship_applications'
    );

    // Check for specific columns
    const [columnCheck] = await pool.query<RowDataPacket[]>(
      `SELECT COLUMN_NAME FROM information_schema.COLUMNS
       WHERE TABLE_SCHEMA = DATABASE()
       AND TABLE_NAME = 'internship_applications'
       AND COLUMN_NAME IN ('student_name', 'student_email', 'university', 'internship_type_preference')`
    );

    return NextResponse.json({
      tableExists: true,
      totalRecords: allRows.length,
      records: allRows,
      tableStructure: tableStructure,
      foundColumns: columnCheck.map(col => col.COLUMN_NAME),
      expectedColumns: ['student_name', 'student_email', 'university', 'internship_type_preference'],
      message: 'Database test successful'
    });
  } catch (error) {
    console.error('Database test error:', error);
    return NextResponse.json({
      error: 'Database connection failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
