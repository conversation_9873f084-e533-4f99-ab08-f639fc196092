import { NextResponse } from 'next/server';
import pool from '@/lib/db';
import { RowDataPacket } from 'mysql2';

interface CountResult extends RowDataPacket {
  total: number;
}

interface ApplicationStats extends RowDataPacket {
  total: number;
  pending: number;
}

export async function GET() {
  try {
    // Get total students (approved applications)
    const [totalStudentsResult] = await pool.query<CountResult[]>(
      "SELECT COUNT(*) as total FROM applications WHERE status = 'approved'"
    );
    const totalStudents = totalStudentsResult[0].total;

    // Get total courses
    const [totalCoursesResult] = await pool.query<CountResult[]>(
      'SELECT COUNT(*) as total FROM courses'
    );

    // Get total pitch deck applications
    const [totalPitchDeckResult] = await pool.query<CountResult[]>(
      'SELECT COUNT(*) as total FROM pitch_deck_applications'
    );
    const totalPitchDeck = totalPitchDeckResult[0]?.total || 0;

    // Get total internship applications
    const [totalInternshipsResult] = await pool.query<CountResult[]>(
      'SELECT COUNT(*) as total FROM internship_applications'
    );
    const totalInternships = totalInternshipsResult[0]?.total || 0;

    // Get total final year project applications
    const [totalFYPResult] = await pool.query<CountResult[]>(
      'SELECT COUNT(*) as total FROM final_year_project_applications'
    );
    const totalFYP = totalFYPResult[0]?.total || 0;
    const totalCourses = totalCoursesResult[0].total;

    // Get application stats
    const [applicationStats] = await pool.query<ApplicationStats[]>(`
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending
      FROM applications
    `);
    const { total: totalApplications, pending: pendingApplications } = applicationStats[0];

    // Get recent applications
    const [recentApplications] = await pool.query(`
      SELECT 
        a.*,
        c.title as course_title
      FROM applications a
      JOIN courses c ON a.course_id = c.course_id
      ORDER BY a.application_date DESC
      LIMIT 5
    `);

    // Get application trend (last 7 days)
    const [applicationTrend] = await pool.query(`
      SELECT 
        DATE(application_date) as date,
        COUNT(*) as applications
      FROM applications
      WHERE application_date >= DATE_SUB(CURRENT_DATE, INTERVAL 7 DAY)
      GROUP BY DATE(application_date)
      ORDER BY date
    `);

    // Get course statistics
    const [courseStats] = await pool.query(`
      SELECT 
        c.title as course_title,
        COUNT(DISTINCT CASE WHEN a.status = 'approved' THEN a.application_id END) as total_students,
        COUNT(DISTINCT CASE WHEN a.status = 'pending' THEN a.application_id END) as pending_applications
      FROM courses c
      LEFT JOIN applications a ON c.course_id = a.course_id
      GROUP BY c.course_id, c.title
      ORDER BY total_students DESC
      LIMIT 5
    `);

    return NextResponse.json({
      totalStudents,
      totalCourses,
      totalApplications,
      pendingApplications,
      totalPitchDeck,
      totalInternships,
      totalFYP,
      recentApplications,
      applicationTrend,
      courseStats
    });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
} 