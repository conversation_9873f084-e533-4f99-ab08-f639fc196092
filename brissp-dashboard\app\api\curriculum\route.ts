import { NextResponse } from 'next/server';
import pool from '@/lib/db';
import { Curriculum } from '@/lib/types';

export async function GET() {
  try {
    const [curriculum] = await pool.query(`
      SELECT c.*, co.title as course_title 
      FROM curriculum c
      LEFT JOIN courses co ON c.course_id = co.course_id
      ORDER BY c.course_id, c.week_number
    `);
    return NextResponse.json(curriculum);
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const body: Curriculum = await request.json();
    const { course_id, week_number, topic, content, learning_objectives } = body;

    const [result] = await pool.query(
      `INSERT INTO curriculum (course_id, week_number, topic, content, learning_objectives)
       VALUES (?, ?, ?, ?, ?)`,
      [course_id, week_number, topic, content, learning_objectives]
    );

    return NextResponse.json({ id: (result as { insertId: number }).insertId }, { status: 201 });
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
} 