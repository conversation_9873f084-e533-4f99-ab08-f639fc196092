"use client"

import React, { useEffect, useState } from 'react';
import CurriculumForm from '@/components/CurriculumForm';
import { Curriculum } from '@/lib/types';
import { useParams } from 'next/navigation';

const CurriculumEdit = () => {
  const params = useParams();
  const id = params?.id; // Use optional chaining to avoid null error
  const [curriculum, setCurriculum] = useState<Curriculum | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      const fetchCurriculum = async () => {
        try {
          const response = await fetch(`/api/curriculum/${id}`);
          if (!response.ok) {
            throw new Error(`Error: ${response.status}`);
          }
          const data = await response.json();
          setCurriculum(data);
        } catch (err) {
          console.error('Failed to fetch curriculum:', err);
          setError('Failed to load curriculum data. Please try again.');
        } finally {
          setIsLoading(false);
        }
      };

      fetchCurriculum();
    }
  }, [id]);

  const handleSubmit = async (data: Partial<Curriculum>) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/curriculum/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      // Use window.location for navigation
      window.location.href = '/curriculum';
    } catch (err) {
      console.error('Failed to update curriculum:', err);
      setError('Failed to update curriculum. Please try again.');
      setIsLoading(false);
    }
  };

  if (isLoading) return <div className="p-6">Loading...</div>;

  if (error) return <div className="p-6 text-red-500">{error}</div>;

  return (
    <div className="p-6 ">
      <h1 className="text-2xl font-bold mb-6">Edit Curriculum</h1>
      <div className="bg-white rounded-lg border p-6">
        {curriculum && (
          <CurriculumForm
            initialData={curriculum}
            onSubmit={handleSubmit}
            isLoading={isLoading}
          />
        )}
        {!curriculum && !isLoading && (
          <div className="text-red-500">Curriculum not found</div>
        )}
      </div>
    </div>
  );
};

export default CurriculumEdit;