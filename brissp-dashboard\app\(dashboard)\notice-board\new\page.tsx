"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import NoticeForm from "@/components/NoticeForm"
import { Notice } from "@/lib/types"

export default function NewNoticePage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (data: Partial<Notice>) => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/notices', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (response.ok) {
        router.push('/notice-board')
      }
    } catch (error) {
      console.error('Error creating notice:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Create New Notice</h1>
      <div className="bg-white rounded-lg border p-6">
        <NoticeForm onSubmit={handleSubmit} isLoading={isLoading} />
      </div>
    </div>
  )
} 