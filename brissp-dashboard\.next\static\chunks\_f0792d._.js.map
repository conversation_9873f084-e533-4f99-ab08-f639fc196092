{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\nexport function formatBytes(bytes: number, decimals = 2) {\r\n  if (bytes === 0) return '0 Bytes';\r\n  \r\n  const k = 1024;\r\n  const dm = decimals < 0 ? 0 : decimals;\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];\r\n  \r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n  \r\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\r\n}\r\n\r\n/**\r\n * Generates a random password with specified length\r\n * @param length Length of the password (default: 10)\r\n * @returns Random password string\r\n */\r\nexport function generateRandomPassword(length: number = 10): string {\r\n  const charset = \"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*\";\r\n  let password = \"\";\r\n  \r\n  for (let i = 0; i < length; i++) {\r\n    const randomIndex = Math.floor(Math.random() * charset.length);\r\n    password += charset[randomIndex];\r\n  }\r\n  \r\n  return password;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa,EAAE,WAAW,CAAC;IACrD,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,KAAK,WAAW,IAAI,IAAI;IAC9B,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;IAEvE,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,OAAO,MAAM,KAAK,CAAC,EAAE;AAC1E;AAOO,SAAS,uBAAuB,SAAiB,EAAE;IACxD,MAAM,UAAU;IAChB,IAAI,WAAW;IAEf,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM;QAC7D,YAAY,OAAO,CAAC,YAAY;IAClC;IAEA,OAAO;AACT"}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,8JAAM,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/components/ui/table.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Table = React.forwardRef<\r\n  HTMLTableElement,\r\n  React.HTMLAttributes<HTMLTableElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div className=\"relative w-full overflow-auto\">\r\n    <table\r\n      ref={ref}\r\n      className={cn(\"w-full caption-bottom text-sm\", className)}\r\n      {...props}\r\n    />\r\n  </div>\r\n))\r\nTable.displayName = \"Table\"\r\n\r\nconst TableHeader = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\r\n))\r\nTableHeader.displayName = \"TableHeader\"\r\n\r\nconst TableBody = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tbody\r\n    ref={ref}\r\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nTableBody.displayName = \"TableBody\"\r\n\r\nconst TableFooter = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tfoot\r\n    ref={ref}\r\n    className={cn(\r\n      \"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTableFooter.displayName = \"TableFooter\"\r\n\r\nconst TableRow = React.forwardRef<\r\n  HTMLTableRowElement,\r\n  React.HTMLAttributes<HTMLTableRowElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tr\r\n    ref={ref}\r\n    className={cn(\r\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTableRow.displayName = \"TableRow\"\r\n\r\nconst TableHead = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.ThHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <th\r\n    ref={ref}\r\n    className={cn(\r\n      \"h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTableHead.displayName = \"TableHead\"\r\n\r\nconst TableCell = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.TdHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <td\r\n    ref={ref}\r\n    className={cn(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nTableCell.displayName = \"TableCell\"\r\n\r\nconst TableCaption = React.forwardRef<\r\n  HTMLTableCaptionElement,\r\n  React.HTMLAttributes<HTMLTableCaptionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <caption\r\n    ref={ref}\r\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nTableCaption.displayName = \"TableCaption\"\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,8JAAM,UAAU,MAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;;AAIf,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAM,KAAK;QAAK,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAa,GAAG,KAAK;;;;;;;AAEzE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,8JAAM,UAAU,OAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,8JAAM,UAAU,OAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,8JAAM,UAAU,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,oGACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,8JAAM,UAAU,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;QAC/D,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,6BAAe,8JAAM,UAAU,QAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;;AAGb,aAAa,WAAW,GAAG"}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Tabs = TabsPrimitive.Root\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsList.displayName = TabsPrimitive.List.displayName\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsContent.displayName = TabsPrimitive.Content.displayName\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,OAAO,oKAAc,IAAI;AAE/B,MAAM,yBAAW,8JAAM,UAAU,MAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAc,IAAI;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,oKAAc,IAAI,CAAC,WAAW;AAErD,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAc,OAAO;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,oKAAc,OAAO,CAAC,WAAW;AAE3D,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAc,OAAO;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,oKAAc,OAAO,CAAC,WAAW"}}, {"offset": {"line": 320, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 326, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\r\n        outline: \"text-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS"}}, {"offset": {"line": 368, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 374, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Dialog = DialogPrimitive.Root\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger\r\n\r\nconst DialogPortal = DialogPrimitive.Portal\r\n\r\nconst DialogClose = DialogPrimitive.Close\r\n\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n))\r\nDialogContent.displayName = DialogPrimitive.Content.displayName\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogHeader.displayName = \"DialogHeader\"\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n)\r\nDialogFooter.displayName = \"DialogFooter\"\r\n\r\nconst DialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-lg font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName\r\n\r\nconst DialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogClose,\r\n  DialogTrigger,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAIA;AAHA;AACA;AAJA;;;;;;AAQA,MAAM,SAAS,sKAAgB,IAAI;AAEnC,MAAM,gBAAgB,sKAAgB,OAAO;AAE7C,MAAM,eAAe,sKAAgB,MAAM;AAE3C,MAAM,cAAc,sKAAgB,KAAK;AAEzC,MAAM,8BAAgB,8JAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;KAVP;AAaN,cAAc,WAAW,GAAG,sKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,8BAAgB,8JAAM,UAAU,OAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,sKAAgB,OAAO;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,sKAAgB,KAAK;wBAAC,WAAU;;0CAC/B,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,sKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,sKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,kCAAoB,8JAAM,UAAU,OAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,WAAW;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,sKAAgB,WAAW,CAAC,WAAW"}}, {"offset": {"line": 519, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/app/%28dashboard%29/applications/components/ApplicationDetailsModal.tsx"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\r\n'use client';\r\n\r\nimport { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from \"@/components/ui/dialog\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { format } from \"date-fns\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\n\r\ninterface ApplicationDetailsProps {\r\n  application: any;\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onApprove: () => void;\r\n  onReject: () => void;\r\n  onWaitlist: () => void;\r\n  isLoading: boolean;\r\n}\r\n\r\nexport function ApplicationDetailsModal({\r\n  application,\r\n  isOpen,\r\n  onClose,\r\n  onApprove,\r\n  onReject,\r\n  onWaitlist,\r\n  isLoading\r\n}: ApplicationDetailsProps) {\r\n  if (!application) return null;\r\n\r\n  const getStatusBadge = (status: string) => {\r\n    const statusStyles = {\r\n      pending: \"bg-yellow-100 text-yellow-800\",\r\n      approved: \"bg-green-100 text-green-800\",\r\n      rejected: \"bg-red-100 text-red-800\",\r\n      waitlisted: \"bg-blue-100 text-blue-800\",\r\n    };\r\n\r\n    return (\r\n      <Badge className={statusStyles[status as keyof typeof statusStyles]}>\r\n        {status.charAt(0).toUpperCase() + status.slice(1)}\r\n      </Badge>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onClose}>\r\n      <DialogContent className=\"max-w-3xl max-h-[90vh] overflow-y-auto\">\r\n        <DialogHeader>\r\n          <DialogTitle>Application Details</DialogTitle>\r\n        </DialogHeader>\r\n\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 py-4\">\r\n          <div className=\"space-y-4\">\r\n            <div>\r\n              <h3 className=\"text-lg font-semibold\">Course Information</h3>\r\n              <p className=\"text-sm text-gray-500\">Course: {application.course_title}</p>\r\n              <p className=\"text-sm text-gray-500\">Student Type: {application.student_type}</p>\r\n              <p className=\"text-sm text-gray-500\">Study Mode: {application.study_mode}</p>\r\n              <p className=\"text-sm text-gray-500\">Academic Year: {application.academic_year}</p>\r\n              <p className=\"text-sm text-gray-500\">Intake: {application.intake}</p>\r\n            </div>\r\n\r\n            <div>\r\n              <h3 className=\"text-lg font-semibold\">Personal Information</h3>\r\n              <p className=\"text-sm text-gray-500\">Name: {application.first_name} {application.last_name} {application.other_names || ''}</p>\r\n              <p className=\"text-sm text-gray-500\">Gender: {application.gender}</p>\r\n              <p className=\"text-sm text-gray-500\">Date of Birth: {format(new Date(application.date_of_birth), 'dd/MM/yyyy')}</p>\r\n              {application.marital_status && (\r\n                <p className=\"text-sm text-gray-500\">Marital Status: {application.marital_status}</p>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"space-y-4\">\r\n            <div>\r\n              <h3 className=\"text-lg font-semibold\">Contact Information</h3>\r\n              <p className=\"text-sm text-gray-500\">Email: {application.email}</p>\r\n              <p className=\"text-sm text-gray-500\">Phone: {application.phone_number}</p>\r\n              <p className=\"text-sm text-gray-500\">Country: {application.country}</p>\r\n            </div>\r\n\r\n            <div>\r\n              <h3 className=\"text-lg font-semibold\">Additional Information</h3>\r\n              <p className=\"text-sm text-gray-500\">Nationality: {application.nationality}</p>\r\n              <p className=\"text-sm text-gray-500\">ID Number: {application.id_number}</p>\r\n            </div>\r\n\r\n            <div>\r\n              <h3 className=\"text-lg font-semibold\">Application Status</h3>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <span className=\"text-sm text-gray-500\">Current Status:</span>\r\n                {getStatusBadge(application.status)}\r\n              </div>\r\n              <p className=\"text-sm text-gray-500\">Application Date: {format(new Date(application.application_date), 'dd/MM/yyyy')}</p>\r\n              {application.review_date && (\r\n                <p className=\"text-sm text-gray-500\">Review Date: {format(new Date(application.review_date), 'dd/MM/yyyy')}</p>\r\n              )}\r\n              {application.review_notes && (\r\n                <div>\r\n                  <p className=\"text-sm text-gray-500\">Review Notes:</p>\r\n                  <p className=\"text-sm p-2 bg-gray-50 rounded\">{application.review_notes}</p>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {application.status === 'pending' && (\r\n          <DialogFooter className=\"flex space-x-2\">\r\n            <Button\r\n              onClick={onApprove}\r\n              disabled={isLoading}\r\n              className=\"bg-green-600 hover:bg-green-700\"\r\n            >\r\n              Approve\r\n            </Button>\r\n            <Button\r\n              onClick={onReject}\r\n              disabled={isLoading}\r\n              variant=\"destructive\"\r\n            >\r\n              Reject\r\n            </Button>\r\n            <Button\r\n              onClick={onWaitlist}\r\n              disabled={isLoading}\r\n              variant=\"outline\"\r\n            >\r\n              Waitlist\r\n            </Button>\r\n            <Button\r\n              onClick={onClose}\r\n              disabled={isLoading}\r\n              variant=\"outline\"\r\n            >\r\n              Close\r\n            </Button>\r\n          </DialogFooter>\r\n        )}\r\n\r\n        {application.status !== 'pending' && (\r\n          <DialogFooter>\r\n            <Button\r\n              onClick={onClose}\r\n              variant=\"outline\"\r\n            >\r\n              Close\r\n            </Button>\r\n          </DialogFooter>\r\n        )}\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}"], "names": [], "mappings": "AAAA,qDAAqD;;;;AAGrD;AACA;AAEA;AADA;AAJA;;;;;;AAiBO,SAAS,wBAAwB,EACtC,WAAW,EACX,MAAM,EACN,OAAO,EACP,SAAS,EACT,QAAQ,EACR,UAAU,EACV,SAAS,EACe;IACxB,IAAI,CAAC,aAAa,OAAO;IAEzB,MAAM,iBAAiB,CAAC;QACtB,MAAM,eAAe;YACnB,SAAS;YACT,UAAU;YACV,UAAU;YACV,YAAY;QACd;QAEA,qBACE,6LAAC,6HAAA,CAAA,QAAK;YAAC,WAAW,YAAY,CAAC,OAAoC;sBAChE,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;;;;;;IAGrD;IAEA,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,8HAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,8HAAA,CAAA,cAAW;kCAAC;;;;;;;;;;;8BAGf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,6LAAC;4CAAE,WAAU;;gDAAwB;gDAAS,YAAY,YAAY;;;;;;;sDACtE,6LAAC;4CAAE,WAAU;;gDAAwB;gDAAe,YAAY,YAAY;;;;;;;sDAC5E,6LAAC;4CAAE,WAAU;;gDAAwB;gDAAa,YAAY,UAAU;;;;;;;sDACxE,6LAAC;4CAAE,WAAU;;gDAAwB;gDAAgB,YAAY,aAAa;;;;;;;sDAC9E,6LAAC;4CAAE,WAAU;;gDAAwB;gDAAS,YAAY,MAAM;;;;;;;;;;;;;8CAGlE,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,6LAAC;4CAAE,WAAU;;gDAAwB;gDAAO,YAAY,UAAU;gDAAC;gDAAE,YAAY,SAAS;gDAAC;gDAAE,YAAY,WAAW,IAAI;;;;;;;sDACxH,6LAAC;4CAAE,WAAU;;gDAAwB;gDAAS,YAAY,MAAM;;;;;;;sDAChE,6LAAC;4CAAE,WAAU;;gDAAwB;gDAAgB,CAAA,GAAA,6LAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,YAAY,aAAa,GAAG;;;;;;;wCAChG,YAAY,cAAc,kBACzB,6LAAC;4CAAE,WAAU;;gDAAwB;gDAAiB,YAAY,cAAc;;;;;;;;;;;;;;;;;;;sCAKtF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,6LAAC;4CAAE,WAAU;;gDAAwB;gDAAQ,YAAY,KAAK;;;;;;;sDAC9D,6LAAC;4CAAE,WAAU;;gDAAwB;gDAAQ,YAAY,YAAY;;;;;;;sDACrE,6LAAC;4CAAE,WAAU;;gDAAwB;gDAAU,YAAY,OAAO;;;;;;;;;;;;;8CAGpE,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,6LAAC;4CAAE,WAAU;;gDAAwB;gDAAc,YAAY,WAAW;;;;;;;sDAC1E,6LAAC;4CAAE,WAAU;;gDAAwB;gDAAY,YAAY,SAAS;;;;;;;;;;;;;8CAGxE,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;gDACvC,eAAe,YAAY,MAAM;;;;;;;sDAEpC,6LAAC;4CAAE,WAAU;;gDAAwB;gDAAmB,CAAA,GAAA,6LAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,YAAY,gBAAgB,GAAG;;;;;;;wCACtG,YAAY,WAAW,kBACtB,6LAAC;4CAAE,WAAU;;gDAAwB;gDAAc,CAAA,GAAA,6LAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,YAAY,WAAW,GAAG;;;;;;;wCAE9F,YAAY,YAAY,kBACvB,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;8DACrC,6LAAC;oDAAE,WAAU;8DAAkC,YAAY,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAOhF,YAAY,MAAM,KAAK,2BACtB,6LAAC,8HAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU;4BACV,SAAQ;sCACT;;;;;;sCAGD,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU;4BACV,SAAQ;sCACT;;;;;;sCAGD,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU;4BACV,SAAQ;sCACT;;;;;;;;;;;;gBAMJ,YAAY,MAAM,KAAK,2BACtB,6LAAC,8HAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAS;wBACT,SAAQ;kCACT;;;;;;;;;;;;;;;;;;;;;;AAQb;KAvIgB"}}, {"offset": {"line": 987, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 993, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/app/%28dashboard%29/applications/page.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useState, useEffect } from \"react\"\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\"\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from \"@/components/ui/tabs\"\r\nimport { Badge } from \"@/components/ui/badge\"\r\nimport { format } from \"date-fns\"\r\nimport { Trash2, Eye } from \"lucide-react\" // Import the eye icon for viewing details\r\nimport { ApplicationDetailsModal } from \"./components/ApplicationDetailsModal\"\r\n\r\ninterface Application {\r\n  application_id: number;\r\n  course_title: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  email: string;\r\n  student_type: 'child' | 'adult';\r\n  study_mode: string;\r\n  status: 'pending' | 'approved' | 'rejected' | 'waitlisted';\r\n  application_date: string;\r\n  review_date?: string;\r\n  review_notes?: string;\r\n  // Add all other fields from the schema\r\n  other_names?: string;\r\n  gender: 'male' | 'female' | 'other';\r\n  marital_status?: 'single' | 'married' | 'divorced' | 'widowed';\r\n  date_of_birth: string;\r\n  nationality: string;\r\n  id_number: string;\r\n  academic_year: string;\r\n  intake: 'january' | 'may' | 'september';\r\n  phone_number: string;\r\n  country: string;\r\n}\r\n\r\nconst ApplicationsPage = () => {\r\n  const [applications, setApplications] = useState<Application[]>([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [selectedApplication, setSelectedApplication] = useState<Application | null>(null);\r\n  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);\r\n\r\n  useEffect(() => {\r\n    fetchApplications();\r\n  }, []);\r\n\r\n  const fetchApplications = async () => {\r\n    try {\r\n      const response = await fetch('/api/applications');\r\n      const data = await response.json();\r\n      setApplications(data);\r\n    } catch (error) {\r\n      console.error('Error fetching applications:', error);\r\n    }\r\n  };\r\n\r\n  const handleStatusUpdate = async (applicationId: number, status: string, notes?: string) => {\r\n    setIsLoading(true);\r\n    try {\r\n      const response = await fetch(`/api/applications/${applicationId}`, {\r\n        method: 'PUT',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          status,\r\n          review_notes: notes,\r\n        }),\r\n      });\r\n\r\n      if (response.ok) {\r\n        if (status === 'approved') {\r\n          const data = await response.json();\r\n          if (data.userCreated) {\r\n            alert(`Application approved! User account created with email: ${data.email}. A welcome email with login details has been sent.`);\r\n          } \r\n        }\r\n        fetchApplications();\r\n        setIsDetailsModalOpen(false);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error updating application:', error);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleDelete = async (applicationId: number) => {\r\n    if (confirm('Are you sure you want to delete this application?')) {\r\n      setIsLoading(true);\r\n      try {\r\n        const response = await fetch(`/api/applications/${applicationId}`, {\r\n          method: 'DELETE',\r\n        });\r\n        if (response.ok) {\r\n          fetchApplications();\r\n        }\r\n      } catch (error) {\r\n        console.error('Error deleting application:', error);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleViewDetails = (application: Application) => {\r\n    setSelectedApplication(application);\r\n    setIsDetailsModalOpen(true);\r\n  };\r\n\r\n  const getStatusBadge = (status: string) => {\r\n    const statusStyles = {\r\n      pending: \"bg-yellow-100 text-yellow-800\",\r\n      approved: \"bg-green-100 text-green-800\",\r\n      rejected: \"bg-red-100 text-red-800\",\r\n      waitlisted: \"bg-blue-100 text-blue-800\",\r\n    };\r\n\r\n    return (\r\n      <Badge className={statusStyles[status as keyof typeof statusStyles]}>\r\n        {status.charAt(0).toUpperCase() + status.slice(1)}\r\n      </Badge>\r\n    );\r\n  };\r\n\r\n  const ApplicationTable = ({ applications }: { applications: Application[] }) => (\r\n    <Table>\r\n      <TableHeader>\r\n        <TableRow>\r\n          <TableHead>Date</TableHead>\r\n          <TableHead>Name</TableHead>\r\n          <TableHead>Course</TableHead>\r\n          <TableHead>Type</TableHead>\r\n          <TableHead>Mode</TableHead>\r\n          <TableHead>Status</TableHead>\r\n          <TableHead>Actions</TableHead>\r\n        </TableRow>\r\n      </TableHeader>\r\n      <TableBody>\r\n        {applications.map((app) => (\r\n          <TableRow key={app.application_id}>\r\n            <TableCell>\r\n              {format(new Date(app.application_date), 'dd/MM/yyyy')}\r\n            </TableCell>\r\n            <TableCell>\r\n              {app.first_name} {app.last_name}\r\n              <div className=\"text-sm text-gray-500\">{app.email}</div>\r\n            </TableCell>\r\n            <TableCell>{app.course_title}</TableCell>\r\n            <TableCell>{app.student_type}</TableCell>\r\n            <TableCell>{app.study_mode}</TableCell>\r\n            <TableCell>{getStatusBadge(app.status)}</TableCell>\r\n            <TableCell>\r\n              <div className=\"flex space-x-2\">\r\n                <Button\r\n                  size=\"sm\"\r\n                  variant=\"outline\"\r\n                  onClick={() => handleViewDetails(app)}\r\n                  className=\"px-2\"\r\n                >\r\n                  <Eye className=\"w-4 h-4\" />\r\n                </Button>\r\n                \r\n                {app.status === 'pending' && (\r\n                  <>\r\n                    <Button\r\n                      size=\"sm\"\r\n                      onClick={() => handleStatusUpdate(app.application_id, 'approved')}\r\n                      disabled={isLoading}\r\n                      className=\"bg-green-600 hover:bg-green-700\"\r\n                    >\r\n                      Approve\r\n                    </Button>\r\n                    <Button\r\n                      size=\"sm\"\r\n                      onClick={() => handleStatusUpdate(app.application_id, 'rejected')}\r\n                      disabled={isLoading}\r\n                      variant=\"destructive\"\r\n                    >\r\n                      Reject\r\n                    </Button>\r\n                    <Button\r\n                      size=\"sm\"\r\n                      onClick={() => handleStatusUpdate(app.application_id, 'waitlisted')}\r\n                      disabled={isLoading}\r\n                      variant=\"outline\"\r\n                    >\r\n                      Waitlist\r\n                    </Button>\r\n                  </>\r\n                )}\r\n                \r\n                {app.status !== 'pending' && (\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"destructive\"\r\n                    onClick={() => handleDelete(app.application_id)}\r\n                    disabled={isLoading}\r\n                  >\r\n                    <Trash2 className=\"w-4 h-4\" />\r\n                  </Button>\r\n                )}\r\n              </div>\r\n            </TableCell>\r\n          </TableRow>\r\n        ))}\r\n      </TableBody>\r\n    </Table>\r\n  );\r\n\r\n  const pendingApplications = applications.filter(app => app.status === 'pending');\r\n  const processedApplications = applications.filter(app => app.status !== 'pending');\r\n\r\n  return (\r\n    <div className=\"p-6\">\r\n      <h1 className=\"text-2xl font-bold mb-6\">Applications Management</h1>\r\n      \r\n      <Tabs defaultValue=\"pending\" className=\"space-y-4\">\r\n        <TabsList>\r\n          <TabsTrigger value=\"pending\">\r\n            Pending Applications ({pendingApplications.length})\r\n          </TabsTrigger>\r\n          <TabsTrigger value=\"processed\">\r\n            Processed Applications ({processedApplications.length})\r\n          </TabsTrigger>\r\n        </TabsList>\r\n\r\n        <TabsContent value=\"pending\" className=\"bg-white rounded-lg border p-4\">\r\n          <h2 className=\"text-lg font-semibold mb-4\">Pending Applications</h2>\r\n          <ApplicationTable applications={pendingApplications} />\r\n        </TabsContent>\r\n\r\n        <TabsContent value=\"processed\" className=\"bg-white rounded-lg border p-4\">\r\n          <h2 className=\"text-lg font-semibold mb-4\">Processed Applications</h2>\r\n          <ApplicationTable applications={processedApplications} />\r\n        </TabsContent>\r\n      </Tabs>\r\n\r\n      {selectedApplication && (\r\n        <ApplicationDetailsModal\r\n          application={selectedApplication}\r\n          isOpen={isDetailsModalOpen}\r\n          onClose={() => setIsDetailsModalOpen(false)}\r\n          onApprove={() => handleStatusUpdate(selectedApplication.application_id, 'approved')}\r\n          onReject={() => handleStatusUpdate(selectedApplication.application_id, 'rejected')}\r\n          onWaitlist={() => handleStatusUpdate(selectedApplication.application_id, 'waitlisted')}\r\n          isLoading={isLoading}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ApplicationsPage;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAGA;AAFA;AACA,+UAA2C,0CAA0C;AAArF;;;AARA;;;;;;;;;AAoCA,MAAM,mBAAmB;;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACnF,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;QACF;qCAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,gBAAgB;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,qBAAqB,OAAO,eAAuB,QAAgB;QACvE,aAAa;QACb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,kBAAkB,EAAE,eAAe,EAAE;gBACjE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,cAAc;gBAChB;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,IAAI,WAAW,YAAY;oBACzB,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,IAAI,KAAK,WAAW,EAAE;wBACpB,MAAM,CAAC,uDAAuD,EAAE,KAAK,KAAK,CAAC,mDAAmD,CAAC;oBACjI;gBACF;gBACA;gBACA,sBAAsB;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,QAAQ,sDAAsD;YAChE,aAAa;YACb,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,kBAAkB,EAAE,eAAe,EAAE;oBACjE,QAAQ;gBACV;gBACA,IAAI,SAAS,EAAE,EAAE;oBACf;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;YAC/C,SAAU;gBACR,aAAa;YACf;QACF;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,uBAAuB;QACvB,sBAAsB;IACxB;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,eAAe;YACnB,SAAS;YACT,UAAU;YACV,UAAU;YACV,YAAY;QACd;QAEA,qBACE,6LAAC,6HAAA,CAAA,QAAK;YAAC,WAAW,YAAY,CAAC,OAAoC;sBAChE,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;;;;;;IAGrD;IAEA,MAAM,mBAAmB,CAAC,EAAE,YAAY,EAAmC,iBACzE,6LAAC,6HAAA,CAAA,QAAK;;8BACJ,6LAAC,6HAAA,CAAA,cAAW;8BACV,cAAA,6LAAC,6HAAA,CAAA,WAAQ;;0CACP,6LAAC,6HAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,6HAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,6HAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,6HAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,6HAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,6HAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,6HAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;;;;;;;8BAGf,6LAAC,6HAAA,CAAA,YAAS;8BACP,aAAa,GAAG,CAAC,CAAC,oBACjB,6LAAC,6HAAA,CAAA,WAAQ;;8CACP,6LAAC,6HAAA,CAAA,YAAS;8CACP,CAAA,GAAA,6LAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,IAAI,gBAAgB,GAAG;;;;;;8CAE1C,6LAAC,6HAAA,CAAA,YAAS;;wCACP,IAAI,UAAU;wCAAC;wCAAE,IAAI,SAAS;sDAC/B,6LAAC;4CAAI,WAAU;sDAAyB,IAAI,KAAK;;;;;;;;;;;;8CAEnD,6LAAC,6HAAA,CAAA,YAAS;8CAAE,IAAI,YAAY;;;;;;8CAC5B,6LAAC,6HAAA,CAAA,YAAS;8CAAE,IAAI,YAAY;;;;;;8CAC5B,6LAAC,6HAAA,CAAA,YAAS;8CAAE,IAAI,UAAU;;;;;;8CAC1B,6LAAC,6HAAA,CAAA,YAAS;8CAAE,eAAe,IAAI,MAAM;;;;;;8CACrC,6LAAC,6HAAA,CAAA,YAAS;8CACR,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,8HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,kBAAkB;gDACjC,WAAU;0DAEV,cAAA,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;4CAGhB,IAAI,MAAM,KAAK,2BACd;;kEACE,6LAAC,8HAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAS,IAAM,mBAAmB,IAAI,cAAc,EAAE;wDACtD,UAAU;wDACV,WAAU;kEACX;;;;;;kEAGD,6LAAC,8HAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAS,IAAM,mBAAmB,IAAI,cAAc,EAAE;wDACtD,UAAU;wDACV,SAAQ;kEACT;;;;;;kEAGD,6LAAC,8HAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAS,IAAM,mBAAmB,IAAI,cAAc,EAAE;wDACtD,UAAU;wDACV,SAAQ;kEACT;;;;;;;;4CAMJ,IAAI,MAAM,KAAK,2BACd,6LAAC,8HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,aAAa,IAAI,cAAc;gDAC9C,UAAU;0DAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2BA3Db,IAAI,cAAc;;;;;;;;;;;;;;;;IAsEzC,MAAM,sBAAsB,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK;IACtE,MAAM,wBAAwB,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK;IAExE,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA0B;;;;;;0BAExC,6LAAC,4HAAA,CAAA,OAAI;gBAAC,cAAa;gBAAU,WAAU;;kCACrC,6LAAC,4HAAA,CAAA,WAAQ;;0CACP,6LAAC,4HAAA,CAAA,cAAW;gCAAC,OAAM;;oCAAU;oCACJ,oBAAoB,MAAM;oCAAC;;;;;;;0CAEpD,6LAAC,4HAAA,CAAA,cAAW;gCAAC,OAAM;;oCAAY;oCACJ,sBAAsB,MAAM;oCAAC;;;;;;;;;;;;;kCAI1D,6LAAC,4HAAA,CAAA,cAAW;wBAAC,OAAM;wBAAU,WAAU;;0CACrC,6LAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,6LAAC;gCAAiB,cAAc;;;;;;;;;;;;kCAGlC,6LAAC,4HAAA,CAAA,cAAW;wBAAC,OAAM;wBAAY,WAAU;;0CACvC,6LAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,6LAAC;gCAAiB,cAAc;;;;;;;;;;;;;;;;;;YAInC,qCACC,6LAAC,iLAAA,CAAA,0BAAuB;gBACtB,aAAa;gBACb,QAAQ;gBACR,SAAS,IAAM,sBAAsB;gBACrC,WAAW,IAAM,mBAAmB,oBAAoB,cAAc,EAAE;gBACxE,UAAU,IAAM,mBAAmB,oBAAoB,cAAc,EAAE;gBACvE,YAAY,IAAM,mBAAmB,oBAAoB,cAAc,EAAE;gBACzE,WAAW;;;;;;;;;;;;AAKrB;GAvNM;KAAA;uCAyNS"}}, {"offset": {"line": 1461, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}