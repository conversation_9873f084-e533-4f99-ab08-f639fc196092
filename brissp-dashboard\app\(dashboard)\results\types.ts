export interface Course {
  course_id: number;
  title: string;
}

export interface Enrollment {
  enrollment_id: number;
  user_id: number;
  first_name: string;
  last_name: string;
}

export interface Result {
  result_id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  assessment_type: string;
  assessment_title: string;
  score: number;
  max_score: number;
  result_date: string;
  comments: string;
  is_passed: boolean;
}