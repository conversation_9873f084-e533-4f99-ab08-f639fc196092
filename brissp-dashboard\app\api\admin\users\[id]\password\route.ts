import { NextResponse, NextRequest } from 'next/server';
import pool from '@/lib/db';
import { authMiddleware } from '@/lib/auth';

export async function PUT(
  request: NextRequest,
) {
  const userId = request.url.split('/').pop(); // Extract userId from URL
  
  if (!userId) {
    return NextResponse.json(
      { error: 'User ID not found in URL' },
      { status: 400 }
    );
  }

  try {
    // Check if user is authenticated
    const authError = await authMiddleware(request);
    if (authError) return authError;
    
    // Parse the request body first
    const { password } = await request.json();
    
    // Validate input
    if (!password) {
      return NextResponse.json(
        { error: 'Password is required' },
        { status: 400 }
      );
    }
    
    // Update user password directly without any hash function
    try {
      // Direct password update without hash_password function
      await pool.query(
        'UPDATE admin_users SET password = ? WHERE admin_id = ?',
        [password, userId]
      );
    } catch (dbError) {
      console.error('Database error:', dbError);
      return NextResponse.json(
        { error: 'Database error: ' + (dbError as Error).message },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ message: 'Password updated successfully' });
  } catch (error) {
    console.error('Update password error:', error);
    return NextResponse.json(
      { error: 'An error occurred while updating password' },
      { status: 500 }
    );
  }
}