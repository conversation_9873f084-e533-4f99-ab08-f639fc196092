-- Minimal fix for internship_applications table
-- This adds only the essential columns needed for the dashboard to work

-- Add the four essential columns that are causing the error
ALTER TABLE internship_applications 
ADD COLUMN IF NOT EXISTS internship_type ENUM('web-development', 'mobile-development', 'data-science', 'ui-ux-design', 'digital-marketing', 'business-analysis', 'other') NOT NULL DEFAULT 'other';

ALTER TABLE internship_applications 
ADD COLUMN IF NOT EXISTS preferred_duration ENUM('3-months', '6-months', '12-months', 'flexible') NOT NULL DEFAULT 'flexible';

ALTER TABLE internship_applications 
ADD COLUMN IF NOT EXISTS availability ENUM('full-time', 'part-time', 'flexible') NOT NULL DEFAULT 'flexible';

ALTER TABLE internship_applications 
ADD COLUMN IF NOT EXISTS remote_preference ENUM('remote', 'on-site', 'hybrid', 'no-preference') NOT NULL DEFAULT 'no-preference';

-- Add education_level which is also required
ALTER TABLE internship_applications 
ADD COLUMN IF NOT EXISTS education_level ENUM('high-school', 'diploma', 'bachelors', 'masters', 'phd', 'other') NOT NULL DEFAULT 'other';

-- Add some basic additional fields that might be useful
ALTER TABLE internship_applications 
ADD COLUMN IF NOT EXISTS institution_name VARCHAR(255);

ALTER TABLE internship_applications 
ADD COLUMN IF NOT EXISTS field_of_study VARCHAR(255);

-- Add indexes for better performance
ALTER TABLE internship_applications 
ADD INDEX IF NOT EXISTS idx_internship_type (internship_type);

-- Update any existing records to have valid values
UPDATE internship_applications 
SET internship_type = 'other' 
WHERE internship_type IS NULL OR internship_type = '';

UPDATE internship_applications 
SET preferred_duration = 'flexible' 
WHERE preferred_duration IS NULL OR preferred_duration = '';

UPDATE internship_applications 
SET availability = 'flexible' 
WHERE availability IS NULL OR availability = '';

UPDATE internship_applications 
SET remote_preference = 'no-preference' 
WHERE remote_preference IS NULL OR remote_preference = '';

UPDATE internship_applications 
SET education_level = 'other' 
WHERE education_level IS NULL OR education_level = '';

-- Verify the changes
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'internship_applications' 
AND COLUMN_NAME IN ('internship_type', 'preferred_duration', 'availability', 'remote_preference', 'education_level');
