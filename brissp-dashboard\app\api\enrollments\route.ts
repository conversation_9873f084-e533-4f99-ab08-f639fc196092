import { NextResponse } from 'next/server';
import pool from '@/lib/db';
import { RowDataPacket } from 'mysql2/promise';

interface Enrollment extends RowDataPacket {
  enrollment_id: number;
  user_id: number;
  course_id: number;
  status: string;
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const courseId = searchParams.get('courseId');
    const status = searchParams.get('status');

    if (!courseId) {
      return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 });
    }

    let query = `
      SELECT e.*, u.first_name, u.last_name 
      FROM user_course_enrollments e
      JOIN users u ON e.user_id = u.user_id
      WHERE e.course_id = ?
    `;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const params: any[] = [courseId];

    if (userId) {
      query += ' AND e.user_id = ?';
      params.push(userId);
    }

    if (status) {
      query += ' AND e.status = ?';
      params.push(status);
    }

    const [enrollments] = await pool.query<Enrollment[]>(query, params);

    return NextResponse.json(enrollments);
  } catch (error) {
    console.error('Error fetching enrollment:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
