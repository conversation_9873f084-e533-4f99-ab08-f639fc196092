"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import CurriculumForm from "@/components/CurriculumForm"
import { Curriculum } from "@/lib/types"

export default function NewCurriculumPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (data: Partial<Curriculum>) => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/curriculum', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (response.ok) {
        router.push('/curriculum')
      }
    } catch (error) {
      console.error('Error creating curriculum:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="p-6 ">
      <h1 className="text-2xl font-bold mb-6">Create New Curriculum</h1>
      <div className="bg-white rounded-lg border p-6">
        <CurriculumForm onSubmit={handleSubmit} isLoading={isLoading} />
      </div>
    </div>
  )
} 