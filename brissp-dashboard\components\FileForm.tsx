"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { DownloadableFile, Course } from "@/lib/types";



interface FileFormProps {
  initialData?: DownloadableFile;
  onSubmit: (data: Partial<DownloadableFile>, fileObject?: File) => void;
  isLoading: boolean;
}

export default function FileForm({
  initialData,
  onSubmit,
  isLoading,
}: FileFormProps) {
  const [formData, setFormData] = useState<Partial<DownloadableFile>>(
    initialData || {
      file_name: "",
      description: "",
      course_id: undefined,
      file_url: "",
    }
  );

  const [fileObject, setFileObject] = useState<File | undefined>(undefined);
  const [courses, setCourses] = useState<Course[]>([]);

  useEffect(() => {
    // Fetch available courses for dropdown
    const fetchCourses = async () => {
      try {
        const response = await fetch('/api/courses');
        const data = await response.json();
        setCourses(data);
      } catch (error) {
        console.error('Error fetching courses:', error);
      }
    };

    fetchCourses();
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData, fileObject);
  };

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: name === "course_id" ? (value ? parseInt(value) : undefined) : value,
    }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    setFileObject(file);
    
    // Auto-fill file name if empty
    if (file && !formData.file_name) {
      setFormData((prev) => ({
        ...prev,
        file_name: file.name,
      }));
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium mb-2">File Name</label>
          <Input
            name="file_name"
            value={formData.file_name}
            onChange={handleChange}
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-2">Associated Course (Optional)</label>
          <select
            name="course_id"
            value={formData.course_id || ""}
            onChange={handleChange}
            className="w-full border rounded-md p-2"
            title="Select associated course"
          >
            <option value="">None (General File)</option>
            {courses.map((course) => (
              <option key={course.course_id} value={course.course_id}>
                {course.title}
              </option>
            ))}
          </select>
        </div>
        <div className="col-span-2">
          <label className="block text-sm font-medium mb-2">Description (Optional)</label>
          <Textarea
            name="description"
            value={formData.description || ""}
            onChange={handleChange}
            rows={3}
          />
        </div>
        <div className="col-span-2">
          <label className="block text-sm font-medium mb-2">Upload File</label>
          <Input 
            type="file" 
            onChange={handleFileChange} 
            required={!initialData?.file_url}
          />
          {initialData?.file_url && (
            <p className="mt-2 text-sm text-gray-500">
              Current file: <a href={initialData.file_url} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">{initialData.file_name}</a>
            </p>
          )}
        </div>
      </div>
      <div className="flex justify-end space-x-4">
        <Button type="submit" disabled={isLoading}>
          {isLoading ? "Saving..." : "Save File"}
        </Button>
      </div>
    </form>
  );
}