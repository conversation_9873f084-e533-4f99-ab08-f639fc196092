import { NextResponse } from 'next/server';
import pool from '@/lib/db';

export async function POST() {
  return createAdmin();
}

export async function GET() {
  return createAdmin();
}

async function createAdmin() {
  try {
    const email = '<EMAIL>';
    const password = 'admin123'; // Plain password - MySQL will store it as is
    const fullName = 'Super Admin';
    
    // Check if admin already exists
    const [existingUsers] = await pool.query(
      'SELECT admin_id FROM admin_users WHERE email = ?',
      [email]
    );
    
    if ((existingUsers as any[]).length > 0) {
      // Update existing admin with plain password
      await pool.query(
        'UPDATE admin_users SET password = ? WHERE email = ?',
        [password, email]
      );
      return NextResponse.json({ 
        message: 'Admin password updated successfully',
        credentials: { email, password }
      });
    } else {
      // Create new admin with plain password
      await pool.query(
        'INSERT INTO admin_users (email, password, full_name, is_super_admin) VALUES (?, ?, ?, ?)',
        [email, password, fullName, true]
      );
      return NextResponse.json({ 
        message: 'Admin user created successfully',
        credentials: { email, password }
      });
    }
  } catch (error) {
    console.error('Error creating admin user:', error);
    return NextResponse.json(
      { error: 'An error occurred while creating admin user: ' + (error as Error).message },
      { status: 500 }
    );
  }
}
