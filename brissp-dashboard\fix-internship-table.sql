-- Fix internship_applications table by adding missing columns
-- Run this if you get "Unknown column 'internship_type' in 'where clause'" error

-- First, check if the table exists and what columns it has
-- DESCRIBE internship_applications;

-- Add missing columns if they don't exist
-- Note: MySQL will give an error if column already exists, so run these one by one

-- Add internship_type column
ALTER TABLE internship_applications 
ADD COLUMN internship_type ENUM('web-development', 'mobile-development', 'data-science', 'ui-ux-design', 'digital-marketing', 'business-analysis', 'other') NOT NULL DEFAULT 'other';

-- Add preferred_duration column
ALTER TABLE internship_applications 
ADD COLUMN preferred_duration ENUM('3-months', '6-months', '12-months', 'flexible') NOT NULL DEFAULT 'flexible';

-- Add availability column
ALTER TABLE internship_applications 
ADD COLUMN availability ENUM('full-time', 'part-time', 'flexible') NOT NULL DEFAULT 'flexible';

-- Add remote_preference column
ALTER TABLE internship_applications 
ADD COLUMN remote_preference ENUM('remote', 'on-site', 'hybrid', 'no-preference') NOT NULL DEFAULT 'no-preference';

-- Add other commonly needed columns
ALTER TABLE internship_applications 
ADD COLUMN date_of_birth DATE;

ALTER TABLE internship_applications 
ADD COLUMN gender ENUM('male', 'female', 'other', 'prefer-not-to-say');

ALTER TABLE internship_applications 
ADD COLUMN nationality VARCHAR(100);

ALTER TABLE internship_applications 
ADD COLUMN address TEXT;

ALTER TABLE internship_applications 
ADD COLUMN city VARCHAR(100);

ALTER TABLE internship_applications 
ADD COLUMN province VARCHAR(100);

ALTER TABLE internship_applications 
ADD COLUMN postal_code VARCHAR(20);

ALTER TABLE internship_applications 
ADD COLUMN emergency_contact_name VARCHAR(255);

ALTER TABLE internship_applications 
ADD COLUMN emergency_contact_phone VARCHAR(50);

ALTER TABLE internship_applications 
ADD COLUMN emergency_contact_relationship VARCHAR(100);

ALTER TABLE internship_applications 
ADD COLUMN education_level ENUM('high-school', 'diploma', 'bachelors', 'masters', 'phd', 'other') NOT NULL DEFAULT 'other';

ALTER TABLE internship_applications 
ADD COLUMN institution_name VARCHAR(255);

ALTER TABLE internship_applications 
ADD COLUMN field_of_study VARCHAR(255);

ALTER TABLE internship_applications 
ADD COLUMN graduation_year YEAR;

ALTER TABLE internship_applications 
ADD COLUMN current_gpa DECIMAL(3,2);

ALTER TABLE internship_applications 
ADD COLUMN relevant_coursework TEXT;

ALTER TABLE internship_applications 
ADD COLUMN programming_languages TEXT;

ALTER TABLE internship_applications 
ADD COLUMN technical_skills TEXT;

ALTER TABLE internship_applications 
ADD COLUMN soft_skills TEXT;

ALTER TABLE internship_applications 
ADD COLUMN previous_experience TEXT;

ALTER TABLE internship_applications 
ADD COLUMN portfolio_url VARCHAR(500);

ALTER TABLE internship_applications 
ADD COLUMN github_url VARCHAR(500);

ALTER TABLE internship_applications 
ADD COLUMN linkedin_url VARCHAR(500);

ALTER TABLE internship_applications 
ADD COLUMN cv_file_url VARCHAR(500);

ALTER TABLE internship_applications 
ADD COLUMN cover_letter TEXT;

ALTER TABLE internship_applications 
ADD COLUMN preferred_start_date DATE;

ALTER TABLE internship_applications 
ADD COLUMN salary_expectation VARCHAR(100);

ALTER TABLE internship_applications 
ADD COLUMN transport_availability BOOLEAN DEFAULT false;

ALTER TABLE internship_applications 
ADD COLUMN willing_to_relocate BOOLEAN DEFAULT false;

ALTER TABLE internship_applications 
ADD COLUMN career_goals TEXT;

ALTER TABLE internship_applications 
ADD COLUMN why_briisp TEXT;

ALTER TABLE internship_applications 
ADD COLUMN additional_info TEXT;

ALTER TABLE internship_applications 
ADD COLUMN referral_source VARCHAR(100);

-- Add management columns if they don't exist
ALTER TABLE internship_applications 
ADD COLUMN reviewed_by INT;

ALTER TABLE internship_applications 
ADD COLUMN review_date TIMESTAMP NULL;

ALTER TABLE internship_applications 
ADD COLUMN review_notes TEXT;

ALTER TABLE internship_applications 
ADD COLUMN interview_date DATETIME NULL;

ALTER TABLE internship_applications 
ADD COLUMN interview_notes TEXT;

ALTER TABLE internship_applications 
ADD COLUMN interviewer VARCHAR(255);

ALTER TABLE internship_applications 
ADD COLUMN internship_start_date DATE;

ALTER TABLE internship_applications 
ADD COLUMN internship_end_date DATE;

ALTER TABLE internship_applications 
ADD COLUMN supervisor_assigned VARCHAR(255);

ALTER TABLE internship_applications 
ADD COLUMN department_assigned VARCHAR(100);

ALTER TABLE internship_applications 
ADD COLUMN final_evaluation_score DECIMAL(3,2);

ALTER TABLE internship_applications 
ADD COLUMN completion_certificate_url VARCHAR(500);

ALTER TABLE internship_applications 
ADD COLUMN recommendation_letter_url VARCHAR(500);

-- Add indexes for better performance
ALTER TABLE internship_applications 
ADD INDEX idx_internship_type (internship_type);

ALTER TABLE internship_applications 
ADD INDEX idx_education_level (education_level);

ALTER TABLE internship_applications 
ADD INDEX idx_preferred_start_date (preferred_start_date);

-- Update existing records to have valid enum values
UPDATE internship_applications 
SET internship_type = 'other' 
WHERE internship_type IS NULL;

UPDATE internship_applications 
SET preferred_duration = 'flexible' 
WHERE preferred_duration IS NULL;

UPDATE internship_applications 
SET availability = 'flexible' 
WHERE availability IS NULL;

UPDATE internship_applications 
SET remote_preference = 'no-preference' 
WHERE remote_preference IS NULL;

UPDATE internship_applications 
SET education_level = 'other' 
WHERE education_level IS NULL;
