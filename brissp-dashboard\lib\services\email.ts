import nodemailer from 'nodemailer';

// Email configuration
const emailConfig = {
  host: process.env.SMTP_HOST,
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: process.env.SMTP_SECURE === 'true',
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASSWORD,
  },
};

// Create reusable transporter
const transporter = nodemailer.createTransport(emailConfig);

// Add debug logging to the transporter creation
console.log('Email config:', {
  host: process.env.SMTP_HOST,
  port: process.env.SMTP_PORT,
  secure: process.env.SMTP_SECURE,
  auth: {
    user: process.env.SMTP_USER,
    // Don't log the actual password
    pass: '***'
  }
});

interface EmailContent {
  to: string;
  subject: string;
  html: string;
  text: string;
}

/**
 * Creates welcome email content for new users
 */
export function createWelcomeEmail(
  email: string,
  firstName: string,
  password: string
): EmailContent {
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2>Welcome to Academy, ${firstName}!</h2>
      
      <p>Your application has been approved, and we're excited to have you join us. Below are your login credentials:</p>
      
      <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <p><strong>Email:</strong> ${email}</p>
        <p><strong>Password:</strong> ${password}</p>
      </div>
      
      <p>For security reasons, we recommend changing your password after your first login.</p>
      
      <p>You can log in at: <a href="${process.env.NEXT_PUBLIC_APP_URL}/login">${process.env.NEXT_PUBLIC_APP_URL}/login</a></p>
      
      <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
      
      <p>Best regards,<br>The Academy Team</p>
    </div>
  `;

  const text = `
    Welcome to Academy, ${firstName}!

    Your application has been approved, and we're excited to have you join us. Below are your login credentials:

    Email: ${email}
    Password: ${password}

    For security reasons, we recommend changing your password after your first login.

    You can log in at: ${process.env.NEXT_PUBLIC_APP_URL}/login

    If you have any questions or need assistance, please don't hesitate to contact our support team.

    Best regards,
    The Academy Team
  `;

  return {
    to: email,
    subject: 'Welcome to Academy - Your Login Credentials',
    html,
    text
  };
}

/**
 * Sends an email using the configured transporter
 */
export async function sendEmail(content: EmailContent): Promise<void> {
  try {
    console.log('Attempting to verify email connection...');
    await transporter.verify();
    console.log('Email connection verified successfully');

    console.log('Attempting to send email to:', content.to);
    const result = await transporter.sendMail({
      from: `"Academy" <${process.env.SMTP_USER}>`,
      ...content
    });
    console.log('Email sent successfully:', result.messageId);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) { // Type assertion for nodemailer errors
    console.error('Detailed email error:', {
      name: error?.name,
      message: error?.message,
      stack: error?.stack,
      code: error?.code,
      command: error?.command
    });
    throw new Error(`Failed to send email: ${error?.message || 'Unknown error'}`);
  }
} 