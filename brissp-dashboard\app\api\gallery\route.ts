import { NextResponse } from 'next/server';
import pool from '@/lib/db';
import { Gallery } from '@/lib/types';

export async function GET(request: Request) {
    try {
        // Get query parameters
        const { searchParams } = new URL(request.url);
        const courseId = searchParams.get('courseId');

        let query = 'SELECT * FROM gallery WHERE is_active = true';
        const params: (string | number)[] = [];

        if (courseId) {
            query += ' AND course_id = ?';
            params.push(courseId);
        }

        query += ' ORDER BY display_order ASC';

        const [galleries] = await pool.query(query, params);
        return NextResponse.json(galleries);
    } catch (error) {
        console.error(error);
        return NextResponse.json(
            { error: 'Internal Server Error' },
            { status: 500 }
        );
    }
}

export async function POST(request: Request) {
    try {
        const body: Gallery = await request.json();
        const {
            course_id,
            image_url,
            image_title,
            image_description,
            image_type,
            display_order
        } = body;

        const [result] = await pool.query(
            `INSERT INTO gallery (
                course_id,
                image_url,
                image_title,
                image_description,
                image_type,
                display_order
            ) VALUES (?, ?, ?, ?, ?, ?)`,
            [
                course_id,
                image_url,
                image_title,
                image_description,
                image_type,
                display_order
            ]
        );

        return NextResponse.json(
            { id: (result as { insertId: number }).insertId },
            { status: 201 }
        );
    } catch (error) {
        console.error(error);
        return NextResponse.json(
            { error: 'Internal Server Error' },
            { status: 500 }
        );
    }
}
