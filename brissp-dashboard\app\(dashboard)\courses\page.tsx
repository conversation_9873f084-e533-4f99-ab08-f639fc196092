"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Plus, <PERSON>cil, Trash2, Refresh<PERSON>w, GraduationCap } from "lucide-react"
import Link from "next/link"
import { Course } from "@/lib/types"
import { Badge } from "@/components/ui/badge"

export default function CoursesPage() {
  const [courses, setCourses] = useState<Course[]>([])
  const [filteredCourses, setFilteredCourses] = useState<Course[]>([])
  const [activeTab, setActiveTab] = useState<'all' | 'regular' | 'refresher'>('all')

  useEffect(() => {
    fetchCourses()
  }, [])

  useEffect(() => {
    filterCourses()
  }, [courses, activeTab])

  const fetchCourses = async () => {
    try {
      const response = await fetch('/api/courses')
      const data = await response.json()
      setCourses(data)
    } catch (error) {
      console.error('Error fetching courses:', error)
    }
  }

  const filterCourses = () => {
    switch (activeTab) {
      case 'regular':
        setFilteredCourses(courses.filter(course => course.program_type !== 'refresher'))
        break
      case 'refresher':
        setFilteredCourses(courses.filter(course => course.program_type === 'refresher'))
        break
      default:
        setFilteredCourses(courses)
    }
  }

  const handleDelete = async (courseId: number) => {
    if (confirm('Are you sure you want to delete this course?')) {
      try {
        const response = await fetch(`/api/courses/${courseId}`, {
          method: 'DELETE',
        })
        if (response.ok) {
          fetchCourses()
        }
      } catch (error) {
        console.error('Error deleting course:', error)
      }
    }
  }

  const getProgramTypeBadge = (programType: string) => {
    if (programType === 'refresher') {
      return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Refresher</Badge>
    }
    return <Badge variant="outline">{programType}</Badge>
  }

  return (
    <div className="p-6 ">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Courses Management</h1>
        <div className="flex gap-2">
          <Link href="/courses/new">
            <Button>
              <GraduationCap className="w-4 h-4 mr-2" />
              Add Regular Course
            </Button>
          </Link>
          <Link href="/courses/refresher/new">
            <Button variant="outline">
              <RefreshCw className="w-4 h-4 mr-2" />
              Add Refresher Course
            </Button>
          </Link>
        </div>
      </div>

      {/* Tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              type="button"
              onClick={() => setActiveTab('all')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'all'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              All Courses ({courses.length})
            </button>
            <button
              type="button"
              onClick={() => setActiveTab('regular')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'regular'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Regular Courses ({courses.filter(c => c.program_type !== 'refresher').length})
            </button>
            <button
              type="button"
              onClick={() => setActiveTab('refresher')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'refresher'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Refresher Courses ({courses.filter(c => c.program_type === 'refresher').length})
            </button>
          </nav>
        </div>
      </div>

      <div className="bg-white rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Course Code</TableHead>
              <TableHead>Title</TableHead>
              <TableHead>Department</TableHead>
              <TableHead>Program Type</TableHead>
              <TableHead>Duration</TableHead>
              <TableHead>Price</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredCourses.map((course) => (
              <TableRow key={course.course_id}>
                <TableCell>{course.course_code}</TableCell>
                <TableCell>{course.title}</TableCell>
                <TableCell>{course.department}</TableCell>
                <TableCell>{getProgramTypeBadge(course.program_type)}</TableCell>
                <TableCell>{course.duration_months} months</TableCell>
                <TableCell>${course.price}</TableCell>
                <TableCell className="flex space-x-2">
                  <Link href={
                    course.program_type === 'refresher'
                      ? `/courses/refresher/${course.course_id}/edit`
                      : `/courses/${course.course_id}/edit`
                  }>
                    <Button variant="outline" size="sm">
                      <Pencil className="w-4 h-4" />
                    </Button>
                  </Link>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDelete(course.course_id)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}