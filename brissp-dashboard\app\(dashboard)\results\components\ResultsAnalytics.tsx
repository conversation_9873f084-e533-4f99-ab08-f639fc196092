'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON>A<PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface ResultsAnalyticsProps {
  analytics: {
    averageScore: number;
    passRate: number;
    assessmentTypeBreakdown: Array<{ name: string; value: number }>;
  };
}

export function ResultsAnalytics({ analytics }: ResultsAnalyticsProps) {
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      <Card>
        <CardHeader>
          <CardTitle>Average Score</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold">{analytics.averageScore.toFixed(1)}%</div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Pass Rate</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold">{analytics.passRate.toFixed(1)}%</div>
        </CardContent>
      </Card>

      <Card className="md:col-span-2 lg:col-span-3">
        <CardHeader>
          <CardTitle>Assessment Type Distribution</CardTitle>
        </CardHeader>
        <CardContent className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={analytics.assessmentTypeBreakdown}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="value" fill="#4f46e5" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );
}