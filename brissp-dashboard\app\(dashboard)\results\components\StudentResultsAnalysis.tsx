/* eslint-disable react/no-unescaped-entities */
'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Progress } from "@/components/ui/progress";
import { Label } from "@/components/ui/label";
import { toast } from 'sonner';
import { Course, Result, Enrollment } from '../types';

interface StudentResultsAnalysisProps {
  courses: Course[];
}

export function StudentResultsAnalysis({ courses }: StudentResultsAnalysisProps) {
  const [selectedCourse, setSelectedCourse] = useState<string>('');
  const [students, setStudents] = useState<Enrollment[]>([]);
  const [selectedStudent, setSelectedStudent] = useState<string>('');
  const [studentResults, setStudentResults] = useState<Result[]>([]);
  const [averageScore, setAverageScore] = useState<number>(0);
  const [passRate, setPassRate] = useState<number>(0);

  // Fetch students when course changes
  useEffect(() => {
    if (selectedCourse) {
      fetchStudents();
    }
  }, [selectedCourse]);

  // Fetch results when student changes
  useEffect(() => {
    if (selectedStudent) {
      fetchStudentResults();
    }
  }, [selectedStudent]);

  const fetchStudents = async () => {
    try {
      const response = await fetch(`/api/enrollments?courseId=${selectedCourse}&status=active`);
      const data = await response.json();
      setStudents(data);
      setSelectedStudent(''); // Reset selected student when course changes
    } catch {
      toast.error('Failed to fetch students');
    }
  };

  const fetchStudentResults = async () => {
    try {
      const response = await fetch(`/api/results?courseId=${selectedCourse}&userId=${selectedStudent}`);
      const data = await response.json();
      
      // Handle different API response formats
      const results = data.results || data;
      
      if (Array.isArray(results)) {
        setStudentResults(results);
        calculateStats(results);
      } else {
        setStudentResults([]);
        setAverageScore(0);
        setPassRate(0);
      }
    } catch {
      toast.error('Failed to fetch student results');
      setStudentResults([]);
      setAverageScore(0);
      setPassRate(0);
    }
  };

  const calculateStats = (results: Result[]) => {
    if (results.length === 0) {
      setAverageScore(0);
      setPassRate(0);
      return;
    }

    // Calculate average score as percentage
    const avg = results.reduce((acc, curr) => 
      acc + (curr.score / curr.max_score) * 100, 0) / results.length;
    
    // Calculate pass rate
    const passed = results.filter(r => r.is_passed).length;
    const passRateValue = (passed / results.length) * 100;

    setAverageScore(avg);
    setPassRate(passRateValue);
  };

  const getGradeLetter = (percentage: number): string => {
    if (percentage >= 90) return 'A';
    if (percentage >= 80) return 'B';
    if (percentage >= 70) return 'C';
    if (percentage >= 60) return 'D';
    return 'F';
  };

  const getColorForScore = (percentage: number): string => {
    if (percentage >= 90) return 'text-green-600';
    if (percentage >= 80) return 'text-green-500';
    if (percentage >= 70) return 'text-yellow-500';
    if (percentage >= 60) return 'text-orange-500';
    return 'text-red-500';
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Student Results Analysis</CardTitle>
          <CardDescription>
            View a student's performance in a specific course
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="course-select">Select Course</Label>
              <Select value={selectedCourse} onValueChange={setSelectedCourse}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a course" />
                </SelectTrigger>
                <SelectContent>
                  {courses.map((course) => (
                    <SelectItem key={course.course_id} value={course.course_id.toString()}>
                      {course.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="student-select">Select Student</Label>
              <Select 
                value={selectedStudent} 
                onValueChange={setSelectedStudent}
                disabled={!selectedCourse || students.length === 0}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a student" />
                </SelectTrigger>
                <SelectContent>
                  {students.map((student) => (
                    <SelectItem key={student.user_id} value={student.user_id.toString()}>
                      {`${student.first_name} ${student.last_name}`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {selectedStudent && studentResults.length > 0 && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Average Score</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-baseline space-x-2">
                  <span className={`text-4xl font-bold ${getColorForScore(averageScore)}`}>
                    {averageScore.toFixed(1)}%
                  </span>
                  <span className={`text-2xl ${getColorForScore(averageScore)}`}>
                    ({getGradeLetter(averageScore)})
                  </span>
                </div>
                <Progress 
                  value={averageScore} 
                  className="h-2 mt-2" 
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Pass Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-4xl font-bold">
                  {passRate.toFixed(1)}%
                </div>
                <div className="text-sm text-muted-foreground mt-1">
                  {Math.round(passRate / 100 * studentResults.length)}/{studentResults.length} assessments passed
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Assessment Results</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Assessment</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Score</TableHead>
                    <TableHead>Percentage</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {studentResults.map((result) => {
                    const scorePercentage = (result.score / result.max_score) * 100;
                    return (
                      <TableRow key={result.result_id}>
                        <TableCell className="font-medium">{result.assessment_title}</TableCell>
                        <TableCell className="capitalize">{result.assessment_type}</TableCell>
                        <TableCell>{`${result.score}/${result.max_score}`}</TableCell>
                        <TableCell className={getColorForScore(scorePercentage)}>
                          {scorePercentage.toFixed(1)}%
                        </TableCell>
                        <TableCell>{new Date(result.result_date).toLocaleDateString()}</TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            result.is_passed ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {result.is_passed ? 'Passed' : 'Failed'}
                          </span>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </>
      )}

      {selectedStudent && studentResults.length === 0 && (
        <Card>
          <CardContent className="py-8">
            <div className="text-center text-muted-foreground">
              No results found for this student in the selected course.
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}