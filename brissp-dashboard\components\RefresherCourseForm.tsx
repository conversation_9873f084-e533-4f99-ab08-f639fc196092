"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Course } from "@/lib/types";
import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";


interface RefresherCourseFormProps {
  initialData?: Course;
  onSubmit: (data: Partial<Course>, imageFile?: File) => void;
  isLoading: boolean;
}

export default function RefresherCourseForm({
  initialData,
  onSubmit,
  isLoading,
}: RefresherCourseFormProps) {
  const [formData, setFormData] = useState<Partial<Course>>(
    initialData || {
      title: "",
      course_code: "",
      description: "",
      duration_months: 0,
      price: 0,
      department: "Computer Science",
      category: "adults",
      program_type: "refresher",
      num_lectures: 0,
      skill_level: "intermediate",
      languages: "English",
      class_days: "Evenings & Weekends",
    }
  );

  const [imageFile, setImageFile] = useState<File | null>(null);

  const editor = useEditor({
    extensions: [StarterKit],
    content: formData.description,
    onUpdate: ({ editor }) => {
      setFormData((prev) => ({
        ...prev,
        description: editor.getHTML(),
      }));
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData, imageFile || undefined);
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: name === "duration_months" || name === "price" || name === "num_lectures" 
        ? Number(value) 
        : value,
    }));
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setImageFile(e.target.files[0]);
    }
  };



  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <h3 className="text-lg font-semibold text-blue-800 mb-2">Refresher Course</h3>
        <p className="text-blue-700 text-sm">
          This form is specifically designed for creating refresher courses. These courses are typically 
          designed for professionals looking to update their skills or refresh their knowledge in a specific field.
        </p>
      </div>

      <div className="grid grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium mb-2">Course Title</label>
          <Input
            name="title"
            value={formData.title}
            onChange={handleChange}
            placeholder="e.g., Data Science Bootcamp"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-2">Course Code</label>
          <Input
            name="course_code"
            value={formData.course_code}
            onChange={handleChange}
            placeholder="e.g., REF-DS-001"
            required
          />
          <p className="text-xs text-gray-500 mt-1">Use REF- prefix for refresher courses</p>
        </div>
        
        <div className="col-span-2">
          <label className="block text-sm font-medium mb-2">Description</label>
          <div className="border rounded-md p-2">
            <div className="border-b p-2 mb-2 flex gap-2">
              <button
                type="button"
                onClick={() => editor?.chain().focus().toggleBold().run()}
                className={`p-2 ${
                  editor?.isActive("bold") ? "bg-gray-200" : ""
                } rounded`}
              >
                <strong>B</strong>
              </button>
              <button
                type="button"
                onClick={() => editor?.chain().focus().toggleItalic().run()}
                className={`p-2 ${
                  editor?.isActive("italic") ? "bg-gray-200" : ""
                } rounded`}
              >
                <em>I</em>
              </button>
            </div>
            <EditorContent editor={editor} className="min-h-[100px] p-2" />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Duration (Months)</label>
          <Input
            type="number"
            name="duration_months"
            value={formData.duration_months}
            onChange={handleChange}
            min="1"
            max="12"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-2">Price ($)</label>
          <Input
            type="number"
            name="price"
            value={formData.price}
            onChange={handleChange}
            min="0"
            step="0.01"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Department</label>
          <select     
            title="department"
            name="department"
            value={formData.department}
            onChange={handleChange}
            className="w-full border rounded-md p-2"
            required
          >
            <option value="Computer Science">Computer Science</option>
            <option value="Auto Engineering">Auto Engineering</option>
            <option value="Business">Business</option>
            <option value="Design">Design</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Skill Level</label>
          <select
            title="skill level"
            name="skill_level"
            value={formData.skill_level}
            onChange={handleChange}
            className="w-full border rounded-md p-2"
            required
          >
            <option value="beginner">Beginner</option>
            <option value="intermediate">Intermediate</option>
            <option value="advanced">Advanced</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Number of Lectures</label>
          <Input
            type="number"
            name="num_lectures"
            value={formData.num_lectures}
            onChange={handleChange}
            min="1"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Languages</label>
          <Input
            name="languages"
            value={formData.languages}
            onChange={handleChange}
            placeholder="e.g., English, Spanish"
            required
          />
        </div>

        <div className="col-span-2">
          <label className="block text-sm font-medium mb-2">Class Schedule</label>
          <select
            title="class days"
            name="class_days"
            value={formData.class_days}
            onChange={handleChange}
            className="w-full border rounded-md p-2"
            required
          >
            <option value="Evenings & Weekends">Evenings & Weekends</option>
            <option value="Weekends Only">Weekends Only</option>
            <option value="Evening Classes">Evening Classes</option>
            <option value="Flexible Schedule">Flexible Schedule</option>
          </select>
        </div>

        <div className="col-span-2">
          <label className="block text-sm font-medium mb-2">Course Image</label>
          <Input
            type="file"
            accept="image/*"
            onChange={handleImageChange}
            className="w-full"
          />
          <p className="text-xs text-gray-500 mt-1">Upload a course image (optional)</p>
        </div>


      </div>

      <div className="flex justify-end space-x-4">
        <Button type="button" variant="outline" onClick={() => window.history.back()}>
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? "Creating..." : "Create Refresher Course"}
        </Button>
      </div>
    </form>
  );
}
