import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '@/lib/auth';

export async function GET(req: NextRequest) {
  try {
    console.log("Auth check request received");
    
    // Check if user is authenticated using the correct cookie name
    const token = req.cookies.get('admin_token')?.value;
    console.log("Token found:", !!token);
    
    if (!token) {
      console.log("No token found in cookies");
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }
    
    const decoded = verifyToken(token);
    console.log("Token verification result:", !!decoded);
    
    if (!decoded) {
      console.log("Token verification failed");
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      );
    }
    
    console.log("Authentication successful");
    return NextResponse.json({ 
      authenticated: true,
      user: {
        id: decoded.id,
        email: decoded.email,
        is_super_admin: decoded.is_super_admin
      }
    });
  } catch (error) {
    console.error('Auth check error:', error);
    return NextResponse.json(
      { error: 'Authentication check failed' },
      { status: 500 }
    );
  }
}