"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Eye, Trash2, Users, Clock, CheckCircle, XCircle, FileText } from "lucide-react";
import Link from "next/link";

interface InternshipApplication {
  application_id: number;
  internship_id?: number;
  applicant_name: string;
  email: string;
  phone: string;
  university: string;
  course_of_study: string;
  year_of_study: string;
  gpa?: string;
  cv_url?: string;
  cover_letter?: string;
  portfolio_url?: string;
  linkedin_url?: string;
  github_url?: string;
  availability_start?: string;
  preferred_duration?: string;
  motivation?: string;
  relevant_experience?: string;
  technical_skills?: string;
  soft_skills?: string;
  preferred_industry?: string;
  internship_type_preference?: 'paid' | 'unpaid' | 'both';
  status: 'pending' | 'reviewed' | 'shortlisted' | 'accepted' | 'rejected';
  application_date: string;
  reviewed_by?: number;
  review_date?: string;
  review_notes?: string;
}

export default function InternshipApplicationsPage() {
  const [applications, setApplications] = useState<InternshipApplication[]>([]);
  const [filteredApplications, setFilteredApplications] = useState<InternshipApplication[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'all' | 'pending' | 'under-review' | 'approved' | 'in-progress'>('all');

  useEffect(() => {
    fetchApplications();
  }, []);

  useEffect(() => {
    const filterApplications = () => {
      switch (activeTab) {
        case 'pending':
          setFilteredApplications(applications.filter(app => app.status === 'pending'));
          break;
        case 'under-review':
          setFilteredApplications(applications.filter(app => app.status === 'reviewed'));
          break;
        case 'approved':
          setFilteredApplications(applications.filter(app => app.status === 'accepted'));
          break;
        case 'in-progress':
          setFilteredApplications(applications.filter(app => app.status === 'shortlisted'));
          break;
        default:
          setFilteredApplications(applications);
      }
    };

    filterApplications();
  }, [applications, activeTab]);

  const fetchApplications = async () => {
    try {
      const response = await fetch("/api/internship-applications");
      const data = await response.json();
      setApplications(data.applications || []);
    } catch (error) {
      console.error("Error fetching internship applications:", error);
    }
  };

  const handleDelete = async (applicationId: number) => {
    if (confirm("Are you sure you want to delete this application?")) {
      setIsLoading(true);
      try {
        const response = await fetch(`/api/internship-applications/${applicationId}`, {
          method: "DELETE",
        });
        if (response.ok) {
          fetchApplications();
        }
      } catch (error) {
        console.error("Error deleting internship application:", error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { variant: "secondary" as const, label: "Pending", className: "bg-yellow-100 text-yellow-800" },
      reviewed: { variant: "outline" as const, label: "Under Review", className: "bg-blue-100 text-blue-800" },
      shortlisted: { variant: "default" as const, label: "In Progress", className: "bg-purple-100 text-purple-800" },
      accepted: { variant: "default" as const, label: "Approved", className: "bg-green-100 text-green-800" },
      rejected: { variant: "destructive" as const, label: "Rejected", className: "bg-red-100 text-red-800" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return (
      <Badge className={config.className}>
        {config.label}
      </Badge>
    );
  };

  const getInternshipTypeBadge = (type: string) => {
    const typeColors = {
      "paid": "bg-green-100 text-green-800",
      "unpaid": "bg-blue-100 text-blue-800",
      "both": "bg-purple-100 text-purple-800"
    };

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${typeColors[type as keyof typeof typeColors] || 'bg-gray-100 text-gray-800'}`}>
        {type ? type.charAt(0).toUpperCase() + type.slice(1) : 'N/A'}
      </span>
    );
  };

  // Calculate counts for tabs
  const getApplicationCounts = () => {
    return {
      all: applications.length,
      pending: applications.filter(app => app.status === 'pending').length,
      underReview: applications.filter(app => app.status === 'reviewed').length,
      approved: applications.filter(app => app.status === 'accepted').length,
      inProgress: applications.filter(app => app.status === 'shortlisted').length,
    };
  };

  const counts = getApplicationCounts();

  const ApplicationTable = ({ applications }: { applications: InternshipApplication[] }) => (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Applicant</TableHead>
          <TableHead>University</TableHead>
          <TableHead>Course</TableHead>
          <TableHead>Type Preference</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Applied Date</TableHead>
          <TableHead>Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {applications.map((app) => (
          <TableRow key={app.application_id}>
            <TableCell>
              <div>
                <div className="font-medium">{app.applicant_name}</div>
                <div className="text-sm text-gray-500">{app.email}</div>
              </div>
            </TableCell>
            <TableCell>
              <div>
                <div className="font-medium">{app.university}</div>
                <div className="text-sm text-gray-500">Year {app.year_of_study}</div>
              </div>
            </TableCell>
            <TableCell>
              <div className="max-w-xs truncate" title={app.course_of_study}>
                {app.course_of_study}
              </div>
            </TableCell>
            <TableCell>{getInternshipTypeBadge(app.internship_type_preference)}</TableCell>
            <TableCell>{getStatusBadge(app.status)}</TableCell>
            <TableCell>{new Date(app.application_date).toLocaleDateString()}</TableCell>
            <TableCell>
              <div className="flex space-x-2">
                <Link href={`/internship-applications/${app.application_id}`}>
                  <Button variant="outline" size="sm">
                    <Eye className="w-4 h-4" />
                  </Button>
                </Link>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDelete(app.application_id)}
                  disabled={isLoading}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Internship Applications</h1>
          <p className="text-gray-600">Manage internship applications and track progress</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <div className="bg-white p-6 rounded-lg border shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Applications</p>
              <p className="text-2xl font-bold">{counts.all}</p>
            </div>
            <FileText className="h-8 w-8 text-blue-600" />
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg border shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-2xl font-bold">{counts.pending}</p>
            </div>
            <Clock className="h-8 w-8 text-yellow-600" />
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg border shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Under Review</p>
              <p className="text-2xl font-bold">{counts.underReview}</p>
            </div>
            <Users className="h-8 w-8 text-blue-600" />
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg border shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Approved</p>
              <p className="text-2xl font-bold">{counts.approved}</p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
        </div>
        <div className="bg-white p-6 rounded-lg border shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">In Progress</p>
              <p className="text-2xl font-bold">{counts.inProgress}</p>
            </div>
            <XCircle className="h-8 w-8 text-purple-600" />
          </div>
        </div>
      </div>

      {/* Tabbed Interface */}
      <div className="bg-white rounded-lg border shadow-sm">
        <div className="border-b">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('all')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'all'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              All Applications ({counts.all})
            </button>
            <button
              onClick={() => setActiveTab('pending')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'pending'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Pending ({counts.pending})
            </button>
            <button
              onClick={() => setActiveTab('under-review')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'under-review'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Under Review ({counts.underReview})
            </button>
            <button
              onClick={() => setActiveTab('approved')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'approved'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Approved ({counts.approved})
            </button>
            <button
              onClick={() => setActiveTab('in-progress')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'in-progress'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              In Progress ({counts.inProgress})
            </button>
          </nav>
        </div>

        <div className="p-6">
          {isLoading ? (
            <div className="text-center py-8">Loading applications...</div>
          ) : filteredApplications.length === 0 ? (
            <div className="text-center py-8">
              <p>No applications found for this status.</p>
            </div>
          ) : (
            <ApplicationTable applications={filteredApplications} />
          )}
        </div>
      </div>
    </div>
  );
}