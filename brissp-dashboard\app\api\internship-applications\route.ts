import { NextRequest, NextResponse } from 'next/server';
import pool from '@/lib/db';
import { RowDataPacket, ResultSetHeader } from 'mysql2';

export interface InternshipApplication extends RowDataPacket {
  application_id: number;
  internship_id?: number;
  applicant_name: string;
  email: string;
  phone: string;
  university: string;
  course_of_study: string;
  year_of_study: string;
  gpa?: string;
  cv_url?: string;
  cover_letter?: string;
  portfolio_url?: string;
  linkedin_url?: string;
  github_url?: string;
  availability_start?: Date;
  preferred_duration?: string;
  motivation?: string;
  relevant_experience?: string;
  technical_skills?: string;
  soft_skills?: string;
  preferred_industry?: string;
  internship_type_preference?: 'paid' | 'unpaid' | 'both';
  status: 'pending' | 'reviewed' | 'shortlisted' | 'accepted' | 'rejected';
  application_date: Date;
  reviewed_by?: number;
  review_date?: Date;
  review_notes?: string;
  // Additional fields that exist in the database
  gender?: string;
  nationality?: string;
  address?: string;
  city?: string;
  province?: string;
  postal_code?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  institution_name?: string;
  field_of_study?: string;
  graduation_year?: number;
  current_gpa?: number;
  relevant_coursework?: string;
  preferred_start_date?: Date;
  availability?: string;
  remote_preference?: string;
  salary_expectation?: string;
  transport_availability?: boolean;
  willing_to_relocate?: boolean;
  why_briisp?: string;
  additional_info?: string;
  referral_source?: string;
  interview_notes?: string;
  interviewer?: string;
  internship_start_date?: Date;
  internship_end_date?: Date;
  supervisor_assigned?: string;
  department_assigned?: string;
  final_evaluation_score?: number;
  completion_certificate_url?: string;
  recommendation_letter_url?: string;
  internship_type?: string;
  date_of_birth?: Date;
  emergency_contact_name?: string;
  programming_languages?: string;
  career_goals?: string;
  interview_date?: Date;
  cv_file_url?: string;
  education_level?: string;
}

// GET all internship applications
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const internship_type = searchParams.get('type');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    let query = `
      SELECT *
      FROM internship_applications
    `;
    const params: any[] = [];
    const conditions: string[] = [];

    if (status && status !== 'all') {
      conditions.push('status = ?');
      params.push(status);
    }

    if (internship_type && internship_type !== 'all') {
      conditions.push('internship_type_preference = ?');
      params.push(internship_type);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' ORDER BY application_date DESC LIMIT ? OFFSET ?';
    params.push(limit, offset);

    console.log('Executing query:', query);
    console.log('Query params:', params);

    const [rows] = await pool.query<InternshipApplication[]>(query, params);
    
    console.log('Query result count:', rows.length);
    if (rows.length > 0) {
      console.log('Sample application data:', rows[0]);
    }

    // Get total count for pagination
    let countQuery = 'SELECT COUNT(*) as total FROM internship_applications';
    const countParams: any[] = [];
    
    if (conditions.length > 0) {
      countQuery += ' WHERE ' + conditions.join(' AND ');
      if (status && status !== 'all') countParams.push(status);
      if (internship_type && internship_type !== 'all') countParams.push(internship_type);
    }

    const [countResult] = await pool.query<RowDataPacket[]>(countQuery, countParams);
    const total = countResult[0].total;

    return NextResponse.json({
      applications: rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching internship applications:', error);
    return NextResponse.json(
      { error: 'Failed to fetch applications', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// POST create new internship application
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    // Extract all fields from the request
    const {
      internship_id, student_name, student_email, student_phone, university,
      course_of_study, year_of_study, gpa, cv_url, cover_letter, portfolio_url,
      linkedin_url, github_url, availability_start, preferred_duration, motivation,
      relevant_experience, technical_skills, soft_skills, preferred_industry,
      internship_type_preference
    } = data;

    // Validate required fields
    if (!student_name || !student_email || !student_phone || !university || !course_of_study || !year_of_study) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const [result] = await pool.query<ResultSetHeader>(
      `INSERT INTO internship_applications (
        internship_id, student_name, student_email, student_phone, university,
        course_of_study, year_of_study, gpa, cv_url, cover_letter, portfolio_url,
        linkedin_url, github_url, availability_start, preferred_duration, motivation,
        relevant_experience, technical_skills, soft_skills, preferred_industry,
        internship_type_preference
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        internship_id, student_name, student_email, student_phone, university,
        course_of_study, year_of_study, gpa, cv_url, cover_letter, portfolio_url,
        linkedin_url, github_url, availability_start, preferred_duration, motivation,
        relevant_experience, technical_skills, soft_skills, preferred_industry,
        internship_type_preference
      ]
    );

    return NextResponse.json(
      {
        message: 'Application submitted successfully',
        application_id: result.insertId
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating internship application:', error);
    return NextResponse.json(
      { error: 'Failed to submit application' },
      { status: 500 }
    );
  }
}
