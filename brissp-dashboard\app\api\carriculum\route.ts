import { NextResponse } from 'next/server';
import pool from '@/lib/db';
import { Curriculum } from '@/lib/types';
import { ResultSetHeader, RowDataPacket } from 'mysql2';

export async function GET() {
  try {
    const [curriculum] = await pool.query<(Curriculum & RowDataPacket)[]>('SELECT * FROM curriculum');
    return NextResponse.json(curriculum);
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const body: Curriculum = await request.json();
    const { course_id, week_number, topic, content, learning_objectives } = body;

    const [result] = await pool.query<ResultSetHeader>(
      `INSERT INTO curriculum (course_id, week_number, topic, content, learning_objectives)
       VALUES (?, ?, ?, ?, ?)`,
      [course_id, week_number, topic, content, learning_objectives]
    );

    return NextResponse.json({ id: result.insertId }, { status: 201 });
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}