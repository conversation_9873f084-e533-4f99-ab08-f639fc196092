"use client";
import React, { useEffect, useState } from "react";
import CourseForm from "@/components/CourseForm";
import { Course } from "@/lib/types";
import { useParams } from "next/navigation";

const CoursesEdit = () => {
  const params = useParams();
  const id = params?.id;
  const [course, setCourse] = useState<Course | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (id) {
      const fetchCourse = async () => {
        const response = await fetch(`/api/courses/${id}`);
        const data = await response.json();
        setCourse(data);
        setIsLoading(false);
      };
      fetchCourse();
    }
  }, [id]);

  const handleSubmit = async (data: Partial<Course>, imageFile?: File) => {
    setIsLoading(true);
    const formData = new FormData();
    formData.append("data", JSON.stringify(data));
    if (imageFile) {
      formData.append("image", imageFile);
    }

    await fetch(`/api/courses/${id}`, {
      method: "PUT",
      body: formData,
    });

    // Use window.location for navigation instead of router.push
    window.location.href = "/courses";
  };

  if (isLoading) return <div>Loading...</div>;

  return (
    <div className="p-6 ">
      <h1 className="text-2xl font-bold mb-6">Edit Course</h1>
      <div className="bg-white rounded-lg border p-6">
        {course && (
          <CourseForm
            initialData={course}
            onSubmit={handleSubmit}
            isLoading={isLoading}
          />
        )}
      </div>
    </div>
  );
};

export default CoursesEdit;
