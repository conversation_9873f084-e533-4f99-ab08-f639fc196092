/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextResponse } from 'next/server';
import pool from '@/lib/db';
import { generateRandomPassword } from '@/lib/utils';
import { ResultSetHeader } from 'mysql2';
import bcrypt from 'bcrypt';
import { createWelcomeEmail, sendEmail } from '@/lib/services/email';

export async function PUT(
  request: Request,
) {
  const id = request.url.split('/').pop(); // Extract applicationId from URL
  if (!id) {
    return NextResponse.json(
      { error: 'Application ID not found in URL' },
      { status: 400 }
    );
  }
  const applicationId = parseInt(id);
  
  if (isNaN(applicationId)) {
    return NextResponse.json(
      { error: 'Invalid application ID' },
      { status: 400 }
    );
  }

  try {
    const body = await request.json();
    const { status, review_notes } = body;

    // Start a transaction
    const connection = await pool.getConnection();
    await connection.beginTransaction();

    try {
      // Update application status
      await connection.query(
        `UPDATE applications 
         SET status = ?, review_notes = ?, review_date = NOW() 
         WHERE application_id = ?`,
        [status, review_notes || null, applicationId]
      );

      let userCreated = false;
      let userEmail = '';
      let emailError = null;

      // If application is approved, create user account and enroll in course
      if (status === 'approved') {
        // Get application details
        const [applications] = await connection.query(
          `SELECT * FROM applications WHERE application_id = ?`,
          [applicationId]
        );
        
        if (!applications || (applications as any[]).length === 0) {
          throw new Error('Application not found');
        }
        
        const application = (applications as any[])[0];
        userEmail = application.email;
        
        // Check if user already exists
        const [existingUsers] = await connection.query(
          `SELECT * FROM users WHERE email = ?`,
          [application.email]
        );
        
        let userId;
        let plainPassword = '';
        
        if ((existingUsers as any[]).length === 0) {
          // Generate random password
          plainPassword = generateRandomPassword();
          const hashedPassword = await bcrypt.hash(plainPassword, 10);
          
          // Create user
          const [userResult] = await connection.query(
            `INSERT INTO users (first_name, last_name, email, password)
             VALUES (?, ?, ?, ?)`,
            [application.first_name, application.last_name, application.email, hashedPassword]
          );
          
          userId = (userResult as any).insertId;
          userCreated = true;
          
          // Send welcome email with login credentials
          try {
            console.log('Creating email content for:', application.email);
            const emailContent = createWelcomeEmail(
              application.email,
              application.first_name,
              plainPassword
            );
            console.log('Attempting to send welcome email...');
            await sendEmail(emailContent);
            console.log('Welcome email sent successfully');
          } catch (error: any) {
            emailError = `Failed to send welcome email: ${error?.message || 'Unknown error'}`;
            console.error('Detailed welcome email error:', error);
          }
        } else {
          userId = (existingUsers as any[])[0].user_id;
        }
        
        // Enroll user in course
        await connection.query(
          `INSERT INTO user_course_enrollments (user_id, course_id)
           VALUES (?, ?)
           ON DUPLICATE KEY UPDATE status = 'active'`,
          [userId, application.course_id]
        );
      }

      await connection.commit();
      
      return NextResponse.json({
        success: true,
        userCreated,
        email: userEmail,
        emailError
      });
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    console.error('Error updating application:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: Request,
  // { params }: { params: { applicationId: string } }
) {
  const id = request.url.split('/').pop(); // Extract applicationId from URL
  if (!id) {
    return NextResponse.json(
      { error: 'Application ID not found in URL' },
      { status: 400 }
    );
  }
  const applicationId = parseInt(id);
  
  if (isNaN(applicationId)) {
    return NextResponse.json(
      { error: 'Invalid application ID' },
      { status: 400 }
    );
  }

  try {
    const [applications] = await pool.query(
      `SELECT 
        a.*,
        c.title as course_title,
        c.course_code
      FROM applications a
      JOIN courses c ON a.course_id = c.course_id
      WHERE a.application_id = ?`,
      [applicationId]
    );
    
    if ((applications as any[]).length === 0) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json((applications as any[])[0]);
  } catch (error) {
    console.error('Error fetching application:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
} 


export async function DELETE(request: Request) {
  let connection;
  try {
    const id = request.url.split('/').pop();

    if (!id) {
      return NextResponse.json(
        { error: 'Missing application ID' },
        { status: 400 }
      );
    }

    // Start a transaction
    connection = await pool.getConnection();
    await connection.beginTransaction();

    // Delete related enrollments
    await connection.query(
      'DELETE FROM user_course_enrollments WHERE enrollment_id IN (SELECT enrollment_id FROM applications WHERE application_id = ?)',
      [id]
    );

    // Delete the application
    const [result] = await connection.query<ResultSetHeader>(
      'DELETE FROM applications WHERE application_id = ?',
      [id]
    );

    if (result.affectedRows === 0) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }

    // Commit the transaction
    await connection.commit();
    return NextResponse.json({ message: 'Application and related enrollments deleted successfully' });
  } catch (error) {
    console.error('Error deleting application:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  } finally {
    if (connection) {
      connection.release();
    }
  }
} 