/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextResponse } from 'next/server';
import pool from '@/lib/db';
import { Notice } from '@/lib/types';
import { RowDataPacket } from 'mysql2';

export async function GET(request: Request) {
  const id = request.url.split('/').pop();

  if (!id) {
    return NextResponse.json({ error: 'Missing notice ID' }, { status: 400 });
  }

  try {
    // Use proper type for MySQL2 query results
    const [rows] = await pool.query<RowDataPacket[]>(
      `SELECT * FROM notice_board WHERE notice_id = ?`,
      [id]
    );

    // Check if we have any results
    if (rows.length === 0) {
      return NextResponse.json({ error: 'Notice not found' }, { status: 404 });
    }

    // Cast the first row to Notice type
    const notice = rows[0] as unknown as Notice;

    // Create a new object to format dates properly before sending
    const formattedNotice = {
      ...notice,
      publish_date: notice.publish_date ? new Date(notice.publish_date).toISOString().split('T')[0] : null,
      expiry_date: notice.expiry_date ? new Date(notice.expiry_date).toISOString().split('T')[0] : null,
    };

    return NextResponse.json(formattedNotice);
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function PUT(request: Request) {
  const id = request.url.split('/').pop();

  if (!id) {
    return NextResponse.json({ error: 'Missing notice ID' }, { status: 400 });
  }

  try {
    const body: Notice = await request.json();
    const { title, description, priority, publish_date, expiry_date } = body;

    const [result] = await pool.query(
      `UPDATE notice_board SET
        title = ?, description = ?, priority = ?, 
        publish_date = ?, expiry_date = ?
      WHERE notice_id = ?`,
      [title, description, priority, publish_date, expiry_date, id]
    );

    // Type assertion for result
    const { affectedRows } = result as any;

    if (affectedRows === 0) {
      return NextResponse.json({ error: 'Notice not found' }, { status: 404 });
    }

    return NextResponse.json({ message: 'Notice updated successfully' });
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function DELETE(request: Request) {
  const id = request.url.split('/').pop();

  if (!id) {
    return NextResponse.json({ error: 'Missing notice ID' }, { status: 400 });
  }

  try {
    const [result] = await pool.query(
      'DELETE FROM notice_board WHERE notice_id = ?',
      [id]
    );

    // Type assertion for result
    const { affectedRows } = result as any;

    if (affectedRows === 0) {
      return NextResponse.json({ error: 'Notice not found' }, { status: 404 });
    }

    return NextResponse.json({ message: 'Notice deleted successfully' });
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}