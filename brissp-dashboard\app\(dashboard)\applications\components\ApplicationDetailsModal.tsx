/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";

interface ApplicationDetailsProps {
  application: any;
  isOpen: boolean;
  onClose: () => void;
  onApprove: () => void;
  onReject: () => void;
  onWaitlist: () => void;
  isLoading: boolean;
}

export function ApplicationDetailsModal({
  application,
  isOpen,
  onClose,
  onApprove,
  onReject,
  onWaitlist,
  isLoading
}: ApplicationDetailsProps) {
  if (!application) return null;

  const getStatusBadge = (status: string) => {
    const statusStyles = {
      pending: "bg-yellow-100 text-yellow-800",
      approved: "bg-green-100 text-green-800",
      rejected: "bg-red-100 text-red-800",
      waitlisted: "bg-blue-100 text-blue-800",
    };

    return (
      <Badge className={statusStyles[status as keyof typeof statusStyles]}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Application Details</DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 py-4">
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold">Course Information</h3>
              <p className="text-sm text-gray-500">Course: {application.course_title}</p>
              <p className="text-sm text-gray-500">Student Type: {application.student_type}</p>
              <p className="text-sm text-gray-500">Study Mode: {application.study_mode}</p>
              <p className="text-sm text-gray-500">Academic Year: {application.academic_year}</p>
              <p className="text-sm text-gray-500">Intake: {application.intake}</p>
            </div>

            <div>
              <h3 className="text-lg font-semibold">Personal Information</h3>
              <p className="text-sm text-gray-500">Name: {application.first_name} {application.last_name} {application.other_names || ''}</p>
              <p className="text-sm text-gray-500">Gender: {application.gender}</p>
              <p className="text-sm text-gray-500">Date of Birth: {format(new Date(application.date_of_birth), 'dd/MM/yyyy')}</p>
              {application.marital_status && (
                <p className="text-sm text-gray-500">Marital Status: {application.marital_status}</p>
              )}
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold">Contact Information</h3>
              <p className="text-sm text-gray-500">Email: {application.email}</p>
              <p className="text-sm text-gray-500">Phone: {application.phone_number}</p>
              <p className="text-sm text-gray-500">Country: {application.country}</p>
            </div>

            <div>
              <h3 className="text-lg font-semibold">Additional Information</h3>
              <p className="text-sm text-gray-500">Nationality: {application.nationality}</p>
              <p className="text-sm text-gray-500">ID Number: {application.id_number}</p>
            </div>

            <div>
              <h3 className="text-lg font-semibold">Application Status</h3>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-500">Current Status:</span>
                {getStatusBadge(application.status)}
              </div>
              <p className="text-sm text-gray-500">Application Date: {format(new Date(application.application_date), 'dd/MM/yyyy')}</p>
              {application.review_date && (
                <p className="text-sm text-gray-500">Review Date: {format(new Date(application.review_date), 'dd/MM/yyyy')}</p>
              )}
              {application.review_notes && (
                <div>
                  <p className="text-sm text-gray-500">Review Notes:</p>
                  <p className="text-sm p-2 bg-gray-50 rounded">{application.review_notes}</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {application.status === 'pending' && (
          <DialogFooter className="flex space-x-2">
            <Button
              onClick={onApprove}
              disabled={isLoading}
              className="bg-green-600 hover:bg-green-700"
            >
              Approve
            </Button>
            <Button
              onClick={onReject}
              disabled={isLoading}
              variant="destructive"
            >
              Reject
            </Button>
            <Button
              onClick={onWaitlist}
              disabled={isLoading}
              variant="outline"
            >
              Waitlist
            </Button>
            <Button
              onClick={onClose}
              disabled={isLoading}
              variant="outline"
            >
              Close
            </Button>
          </DialogFooter>
        )}

        {application.status !== 'pending' && (
          <DialogFooter>
            <Button
              onClick={onClose}
              variant="outline"
            >
              Close
            </Button>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
}