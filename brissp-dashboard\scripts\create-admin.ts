/* eslint-disable @typescript-eslint/no-explicit-any */
import pool from '../lib/db';

async function createAdmin() {
  try {
    const email = '<EMAIL>';
    const password = 'admin123'; // Plain password - MySQL will hash it
    const fullName = 'Super Admin';
    
    // Check if admin already exists
    const [existingUsers] = await pool.query(
      'SELECT admin_id FROM admin_users WHERE email = ?',
      [email]
    );
    
    if ((existingUsers as any[]).length > 0) {
      // Update existing admin with plain password
      await pool.query(
        'UPDATE admin_users SET password = ? WHERE email = ?',
        [password, email]
      );
      console.log('Admin password updated successfully');
    } else {
      // Create new admin with plain password
      await pool.query(
        'INSERT INTO admin_users (email, password, full_name, is_super_admin) VALUES (?, ?, ?, ?)',
        [email, password, fullName, true]
      );
      console.log('Admin user created successfully');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error creating admin user:', error);
    process.exit(1);
  }
}

createAdmin();