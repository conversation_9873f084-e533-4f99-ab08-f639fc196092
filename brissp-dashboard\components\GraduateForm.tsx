import React, { useState } from 'react';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Trash2 } from "lucide-react";
import { Graduate,  } from "@/lib/types";

interface GraduateFormProps {
  initialData?: Graduate;
  onSubmit: (data: Partial<Graduate>, certificateFile?: File | null) => Promise<void>;
  isLoading?: boolean;
}



const GraduateForm: React.FC<GraduateFormProps> = ({ initialData, onSubmit, isLoading }) => {
  const [formData, setFormData] = useState({
    course_id: initialData?.course_id ? Number(initialData.course_id) : undefined,
    name: initialData?.name || '',
    email: initialData?.email || '',
    cell_number: initialData?.cell_number || '',
    year_of_completion: initialData?.year_of_completion || new Date().getFullYear(),
    period_of_study: initialData?.period_of_study || '',
    final_score: initialData?.final_score || '',
    certificate_number: initialData?.certificate_number || '',
    projects: initialData?.projects || [{ 
      project_title: '', 
      description: '', 
      github_url: '',
      project_url: '',
      technologies_used: '',
      completion_date: new Date(),
    }],
    social_links: initialData?.social_links || [{
      platform: 'github',
      url: ''
    }]
  });
  const [certificateFile, setCertificateFile] = useState<File | null>(null);
  const [graduateImage, setGraduateImage] = useState<File | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleProjectChange = (index: number, field: string, value: string | Date) => {
    const updatedProjects = [...formData.projects];
    updatedProjects[index] = { ...updatedProjects[index], [field]: field === 'completion_date' ? new Date(value) : value };
    setFormData(prev => ({ ...prev, projects: updatedProjects }));
  };

  const handleSocialLinkChange = (index: number, field: string, value: string) => {
    const updatedLinks = [...formData.social_links];
    updatedLinks[index] = { ...updatedLinks[index], [field]: value };
    setFormData(prev => ({ ...prev, social_links: updatedLinks }));
  };

  const addProject = () => {
    setFormData(prev => ({
      ...prev,
      projects: [...prev.projects, { 
        project_title: '', 
        description: '', 
        github_url: '',
        project_url: '',
        technologies_used: '',
        completion_date: new Date(),
      }]
    }));
  };

  const removeProject = (index: number) => {
    setFormData(prev => ({
      ...prev,
      projects: prev.projects.filter((_, i) => i !== index)
    }));
  };

  const addSocialLink = () => {
    setFormData(prev => ({
      ...prev,
      social_links: [...prev.social_links, { platform: 'github', url: '' }]
    }));
  };

  const removeSocialLink = (index: number) => {
    setFormData(prev => ({
      ...prev,
      social_links: prev.social_links.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Create FormData object to support file uploads
    const submitFormData = new FormData();
    
    if (certificateFile) {
      submitFormData.append('certificate_file', certificateFile);
    }
    if (graduateImage) {
      submitFormData.append('graduate_image', graduateImage);
    }
    
    submitFormData.append('graduateData', JSON.stringify({
      course_id: formData.course_id,
      name: formData.name,
      email: formData.email,
      cell_number: formData.cell_number,
      year_of_completion: formData.year_of_completion,
      period_of_study: formData.period_of_study,
      final_score: typeof formData.final_score === 'string' ? Number(formData.final_score) : formData.final_score,
      certificate_number: formData.certificate_number
    }));
    
    // Add projects and social links as JSON strings
    submitFormData.append('projects', JSON.stringify(formData.projects));
    submitFormData.append('social_links', JSON.stringify(formData.social_links));

    try {
      const response = await fetch('/api/graduates', {
        method: 'POST',
        body: submitFormData,
      });

      if (!response.ok) {
        throw new Error('Failed to submit form');
      }

      // Handle success (e.g., redirect or show success message)
      if (onSubmit) {
        await onSubmit({
          ...formData,
          course_id: typeof formData.course_id === 'string' ? Number(formData.course_id) : formData.course_id,
          final_score: typeof formData.final_score === 'string' ? Number(formData.final_score) : formData.final_score,
        }, certificateFile);
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      // Handle error (e.g., show error message to user)
    }
  };

  return (
    <form onSubmit={handleSubmit} encType="multipart/form-data" className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Name</Label>
          <Input
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            name="email"
            type="email"
            value={formData.email}
            onChange={handleChange}
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="cell_number">Cell Number</Label>
          <Input
            id="cell_number"
            name="cell_number"
            value={formData.cell_number}
            onChange={handleChange}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="year_of_completion">Year of Completion</Label>
          <Input
            id="year_of_completion"
            name="year_of_completion"
            type="number"
            value={formData.year_of_completion}
            onChange={handleChange}
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="period_of_study">Period of Study</Label>
          <Input
            id="period_of_study"
            name="period_of_study"
            value={formData.period_of_study}
            onChange={handleChange}
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="final_score">Final Score</Label>
          <Input
            id="final_score"
            name="final_score"
            type="number"
            step="0.01"
            value={formData.final_score}
            onChange={handleChange}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="certificate_number">Certificate Number</Label>
          <Input
            id="certificate_number"
            name="certificate_number"
            value={formData.certificate_number}
            onChange={handleChange}
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="certificate_file">Certificate File</Label>
          <Input
            id="certificate_file"
            name="certificate_file"
            type="file"
            onChange={(e) => setCertificateFile(e.target.files?.[0] || null)}
            accept=".pdf,.jpg,.jpeg,.png"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="graduate_image">Graduate Image</Label>
          <Input
            id="graduate_image"
            name="graduate_image"
            type="file"
            onChange={(e) => setGraduateImage(e.target.files?.[0] || null)}
            accept="image/*"
          />
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">Projects</h3>
          <Button type="button" onClick={addProject}>Add Project</Button>
        </div>
        
        {formData.projects.map((project, index) => (
          <div key={index} className="border p-4 rounded-lg space-y-4">
            <div className="flex justify-end">
              <Button 
                type="button" 
                variant="destructive" 
                size="sm"
                onClick={() => removeProject(index)}
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Project Title</Label>
                <Input
                  value={project.project_title}
                  onChange={(e) => handleProjectChange(index, 'project_title', e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label>GitHub URL</Label>
                <Input
                  value={project.github_url}
                  onChange={(e) => handleProjectChange(index, 'github_url', e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label>Project URL</Label>
                <Input
                  value={project.project_url}
                  onChange={(e) => handleProjectChange(index, 'project_url', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label>Technologies Used</Label>
                <Input
                  value={project.technologies_used}
                  onChange={(e) => handleProjectChange(index, 'technologies_used', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label>Completion Date</Label>
                <Input
                  type="date"
                  value={project.completion_date ? project.completion_date.toISOString().split('T')[0] : ''}
                  onChange={(e) => handleProjectChange(index, 'completion_date', e.target.value)}
                />
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">Social Links</h3>
          <Button type="button" onClick={addSocialLink}>Add Social Link</Button>
        </div>

        {formData.social_links.map((link, index) => (
          <div key={index} className="flex gap-4 items-end">
            <div className="flex-1 space-y-2">
              <Label>Platform</Label>
              <select
                name="platform"
                value={link.platform}
                onChange={(e) => handleSocialLinkChange(index, 'platform', e.target.value)}
                className="w-full border rounded-md p-2"
                title="Select social media platform"
              >
                <option value="github">GitHub</option>
                <option value="linkedin">LinkedIn</option>
                <option value="twitter">Twitter</option>
                <option value="portfolio">Portfolio</option>
                <option value="other">Other</option>
              </select>
            </div>
            <div className="flex-1 space-y-2">
              <Label>URL</Label>
              <Input
                value={link.url}
                onChange={(e) => handleSocialLinkChange(index, 'url', e.target.value)}
                required
              />
            </div>
            <Button 
              type="button" 
              variant="destructive" 
              size="sm"
              onClick={() => removeSocialLink(index)}
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>
        ))}
      </div>

      <Button type="submit" disabled={isLoading}>
        {isLoading ? 'Saving...' : 'Save Graduate'}
      </Button>
    </form>
  );
};

export default GraduateForm;
