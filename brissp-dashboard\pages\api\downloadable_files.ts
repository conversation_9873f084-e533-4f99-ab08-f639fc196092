import { NextApiRequest, NextApiResponse } from 'next'
import db from '@/lib/db' // Adjust the import based on your database setup

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'GET') {
    const files = await db.query('SELECT * FROM downloadable_files')
    res.status(200).json(files)
  } else if (req.method === 'POST') {
    const { title, file_url, file_type, file_size } = req.body
    await db.query('INSERT INTO downloadable_files (title, file_url, file_type, file_size) VALUES (?, ?, ?, ?)', [title, file_url, file_type, file_size])
    res.status(201).json({ message: 'File added successfully' })
  }
}

// Handle DELETE requests in a separate file or within the same handler based on your preference 