/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextResponse } from 'next/server';
import pool from '@/lib/db';

export async function PUT(request: Request) {
  try {
    const resultId = request.url.split('/').pop(); // Extract resultId from URL
    if (!resultId) {
      return NextResponse.json(
        { error: 'Result ID not found in URL' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const {
      assessment_type,
      assessment_title,
      score,
      max_score,
      result_date,
      comments,
      is_passed
    } = body;

    // Validate required fields
    if (!assessment_type || !assessment_title || score === undefined || !max_score || !result_date) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Format the date to MySQL datetime format (YYYY-MM-DD HH:mm:ss)
    const formattedDate = new Date(result_date).toISOString().slice(0, 19).replace('T', ' ');

    // Validate numeric fields
    const numericScore = parseFloat(score);
    const numericMaxScore = parseFloat(max_score);

    if (isNaN(numericScore) || isNaN(numericMaxScore)) {
      return NextResponse.json(
        { error: 'Score and max score must be valid numbers' },
        { status: 400 }
      );
    }

    // Check if the result exists before updating
    const [existingResult] = await pool.query(
      'SELECT result_id FROM results WHERE result_id = ?',
      [resultId]
    );

    if (!(existingResult as any[])[0]) {
      return NextResponse.json(
        { error: 'Result not found' },
        { status: 404 }
      );
    }

    await pool.query(
      `UPDATE results 
       SET assessment_type = ?, 
           assessment_title = ?, 
           score = ?, 
           max_score = ?, 
           result_date = ?, 
           comments = ?, 
           is_passed = ?
       WHERE result_id = ?`,
      [assessment_type, assessment_title, numericScore, numericMaxScore, formattedDate, comments || null, is_passed || false, resultId]
    );

    // Fetch the updated result to return
    const [updatedResult] = await pool.query(
      `SELECT r.*, u.first_name, u.last_name
       FROM results r
       LEFT JOIN users u ON r.user_id = u.user_id
       WHERE r.result_id = ?`,
      [resultId]
    );

    return NextResponse.json({
      message: 'Result updated successfully',
      result: (updatedResult as any[])[0]
    });
  } catch (error) {
    console.error('Error updating result:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function GET(request: Request) {
  try {
    const resultId = request.url.split('/').pop(); // Extract resultId from URL
    if (!resultId) {
      return NextResponse.json(
        { error: 'Result ID not found in URL' },
        { status: 400 }
      );
    }

    const [results] = await pool.query(
      `SELECT * FROM results WHERE result_id = ?`,
      [resultId]
    );

    if ((results as any[]).length === 0) {
      return NextResponse.json(
        { error: 'Result not found' },
        { status: 404 }
      );
    }

    return NextResponse.json((results as any[])[0]);
  } catch (error) {
    console.error('Error fetching result:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: Request) {
  try {
    const resultId = request.url.split('/').pop(); // Extract resultId from URL
    if (!resultId) {
      return NextResponse.json(
        { error: 'Result ID not found in URL' },
        { status: 400 }
      );
    }

    const [result] = await pool.query(
      'DELETE FROM results WHERE result_id = ?',
      [resultId]
    );

    if ((result as any).affectedRows === 0) {
      return NextResponse.json(
        { error: 'Result not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Result deleted successfully' });
  } catch (error) {
    console.error('Error deleting result:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}