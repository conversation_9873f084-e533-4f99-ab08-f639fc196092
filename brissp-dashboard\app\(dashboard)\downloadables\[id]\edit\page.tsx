"use client"

import React, { useEffect, useState } from 'react';
import FileForm from '@/components/FileForm';
import { DownloadableFile } from '@/lib/types';
import { useParams } from 'next/navigation';

const DownloadableFileEdit = () => {
  const params = useParams();
  const id = params?.id; // Use optional chaining to avoid null error
  const [file, setFile] = useState<DownloadableFile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      const fetchFile = async () => {
        try {
          const response = await fetch(`/api/downloadables/${id}`);
          if (!response.ok) {
            throw new Error(`Error: ${response.status}`);
          }
          const data = await response.json();
          setFile(data);
        } catch (err) {
          console.error('Failed to fetch file:', err);
          setError('Failed to load file data. Please try again.');
        } finally {
          setIsLoading(false);
        }
      };

      fetchFile();
    }
  }, [id]);

  const handleSubmit = async (data: Partial<DownloadableFile>, fileObject?: File) => {
    setIsLoading(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('file_name', data.file_name || '');
      formData.append('file_url', data.file_url || '');
      formData.append('description', data.description || '');
      formData.append('course_id', data.course_id?.toString() || '');
      if (fileObject) {
        formData.append('file', fileObject);
      }

      const response = await fetch(`/api/downloadables/${id}`, {
        method: 'PUT',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      // Use window.location for navigation
      window.location.href = '/downloadables';
    } catch (err) {
      console.error('Failed to update file:', err);
      setError('Failed to update file. Please try again.');
      setIsLoading(false);
    }
  };

  if (isLoading) return <div className="p-6">Loading...</div>;

  if (error) return <div className="p-6 text-red-500">{error}</div>;

  return (
    <div className="p-6 ">
      <h1 className="text-2xl font-bold mb-6">Edit Downloadable File</h1>
      <div className="bg-white rounded-lg border p-6">
        {file && (
          <FileForm
            initialData={file}
            onSubmit={handleSubmit}
            isLoading={isLoading}
          />
        )}
        {!file && !isLoading && (
          <div className="text-red-500">File not found</div>
        )}
      </div>
    </div>
  );
};

export default DownloadableFileEdit;