/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextRequest, NextResponse } from 'next/server';
import pool from '@/lib/db';
import { superAdminMiddleware, hashPassword } from '@/lib/auth';

// Delete an admin user
export async function DELETE(
  req: NextRequest,
  // Removing the params parameter
) {
  try {
    // Check if user is super admin
    const authError = await superAdminMiddleware(req);
    if (authError) return authError;
    
    // Extract userId from URL instead of using params
    const userId = req.url.split('/').pop();
    
    const { full_name, email, is_super_admin, password } = await req.json();
    
    // Check if user exists
    const [existingUsers] = await pool.query(
      'SELECT admin_id, is_super_admin FROM admin_users WHERE admin_id = ?',
      [userId]
    );
    
    const users = existingUsers as any[];
    if (users.length === 0) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    // If changing super admin status, check if it's the last super admin
    if (users[0].is_super_admin && is_super_admin === false) {
      const [superAdmins] = await pool.query(
        'SELECT COUNT(*) as count FROM admin_users WHERE is_super_admin = true'
      );
      
      if ((superAdmins as any[])[0].count <= 1) {
        return NextResponse.json(
          { error: 'Cannot remove super admin status from the last super admin' },
          { status: 400 }
        );
      }
    }
    
    // If email is being changed, check if it's already in use
    if (email) {
      const [emailCheck] = await pool.query(
        'SELECT admin_id FROM admin_users WHERE email = ? AND admin_id != ?',
        [email, userId]
      );
      
      if ((emailCheck as any[]).length > 0) {
        return NextResponse.json(
          { error: 'Email is already in use by another admin' },
          { status: 400 }
        );
      }
    }
    
    // Build update query
    let updateQuery = 'UPDATE admin_users SET ';
    const updateValues = [];
    
    if (full_name) {
      updateQuery += 'full_name = ?, ';
      updateValues.push(full_name);
    }
    
    if (email) {
      updateQuery += 'email = ?, ';
      updateValues.push(email);
    }
    
    if (is_super_admin !== undefined) {
      updateQuery += 'is_super_admin = ?, ';
      updateValues.push(is_super_admin);
    }
    
    if (password) {
      const hashedPwd = await hashPassword(password);
      updateQuery += 'password = ?, ';
      updateValues.push(hashedPwd);
    }
    
    // Remove trailing comma and space
    updateQuery = updateQuery.slice(0, -2);
    
    // Add WHERE clause
    updateQuery += ' WHERE admin_id = ?';
    updateValues.push(userId);
    
    // Execute update if there are fields to update
    if (updateValues.length > 1) { // At least one field plus the userId
      await pool.query(updateQuery, updateValues);
      
      return NextResponse.json({
        message: 'Admin user updated successfully'
      });
    } else {
      return NextResponse.json({
        message: 'No fields to update'
      });
    }
  } catch (error) {
    console.error('Update admin user error:', error);
    return NextResponse.json(
      { error: 'An error occurred while updating admin user' },
      { status: 500 }
    );
  }
}