"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { format } from "date-fns"
import { Trash2, Eye } from "lucide-react" // Import the eye icon for viewing details
import { ApplicationDetailsModal } from "./components/ApplicationDetailsModal"

interface Application {
  application_id: number;
  course_title: string;
  first_name: string;
  last_name: string;
  email: string;
  student_type: 'child' | 'adult';
  study_mode: string;
  status: 'pending' | 'approved' | 'rejected' | 'waitlisted';
  application_date: string;
  review_date?: string;
  review_notes?: string;
  // Add all other fields from the schema
  other_names?: string;
  gender: 'male' | 'female' | 'other';
  marital_status?: 'single' | 'married' | 'divorced' | 'widowed';
  date_of_birth: string;
  nationality: string;
  id_number: string;
  academic_year: string;
  intake: 'january' | 'may' | 'september';
  phone_number: string;
  country: string;
}

const ApplicationsPage = () => {
  const [applications, setApplications] = useState<Application[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedApplication, setSelectedApplication] = useState<Application | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);

  useEffect(() => {
    fetchApplications();
  }, []);

  const fetchApplications = async () => {
    try {
      const response = await fetch('/api/applications');
      const data = await response.json();
      setApplications(data);
    } catch (error) {
      console.error('Error fetching applications:', error);
    }
  };

  const handleStatusUpdate = async (applicationId: number, status: string, notes?: string) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/applications/${applicationId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status,
          review_notes: notes,
        }),
      });

      if (response.ok) {
        if (status === 'approved') {
          const data = await response.json();
          if (data.userCreated) {
            alert(`Application approved! User account created with email: ${data.email}. A welcome email with login details has been sent.`);
          } 
        }
        fetchApplications();
        setIsDetailsModalOpen(false);
      }
    } catch (error) {
      console.error('Error updating application:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (applicationId: number) => {
    if (confirm('Are you sure you want to delete this application?')) {
      setIsLoading(true);
      try {
        const response = await fetch(`/api/applications/${applicationId}`, {
          method: 'DELETE',
        });
        if (response.ok) {
          fetchApplications();
        }
      } catch (error) {
        console.error('Error deleting application:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleViewDetails = (application: Application) => {
    setSelectedApplication(application);
    setIsDetailsModalOpen(true);
  };

  const getStatusBadge = (status: string) => {
    const statusStyles = {
      pending: "bg-yellow-100 text-yellow-800",
      approved: "bg-green-100 text-green-800",
      rejected: "bg-red-100 text-red-800",
      waitlisted: "bg-blue-100 text-blue-800",
    };

    return (
      <Badge className={statusStyles[status as keyof typeof statusStyles]}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const ApplicationTable = ({ applications }: { applications: Application[] }) => (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Date</TableHead>
          <TableHead>Name</TableHead>
          <TableHead>Course</TableHead>
          <TableHead>Type</TableHead>
          <TableHead>Mode</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {applications.map((app) => (
          <TableRow key={app.application_id}>
            <TableCell>
              {format(new Date(app.application_date), 'dd/MM/yyyy')}
            </TableCell>
            <TableCell>
              {app.first_name} {app.last_name}
              <div className="text-sm text-gray-500">{app.email}</div>
            </TableCell>
            <TableCell>{app.course_title}</TableCell>
            <TableCell>{app.student_type}</TableCell>
            <TableCell>{app.study_mode}</TableCell>
            <TableCell>{getStatusBadge(app.status)}</TableCell>
            <TableCell>
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleViewDetails(app)}
                  className="px-2"
                >
                  <Eye className="w-4 h-4" />
                </Button>
                
                {app.status === 'pending' && (
                  <>
                    <Button
                      size="sm"
                      onClick={() => handleStatusUpdate(app.application_id, 'approved')}
                      disabled={isLoading}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      Approve
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => handleStatusUpdate(app.application_id, 'rejected')}
                      disabled={isLoading}
                      variant="destructive"
                    >
                      Reject
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => handleStatusUpdate(app.application_id, 'waitlisted')}
                      disabled={isLoading}
                      variant="outline"
                    >
                      Waitlist
                    </Button>
                  </>
                )}
                
                {app.status !== 'pending' && (
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => handleDelete(app.application_id)}
                    disabled={isLoading}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                )}
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );

  const pendingApplications = applications.filter(app => app.status === 'pending');
  const processedApplications = applications.filter(app => app.status !== 'pending');

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Applications Management</h1>
      
      <Tabs defaultValue="pending" className="space-y-4">
        <TabsList>
          <TabsTrigger value="pending">
            Pending Applications ({pendingApplications.length})
          </TabsTrigger>
          <TabsTrigger value="processed">
            Processed Applications ({processedApplications.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="pending" className="bg-white rounded-lg border p-4">
          <h2 className="text-lg font-semibold mb-4">Pending Applications</h2>
          <ApplicationTable applications={pendingApplications} />
        </TabsContent>

        <TabsContent value="processed" className="bg-white rounded-lg border p-4">
          <h2 className="text-lg font-semibold mb-4">Processed Applications</h2>
          <ApplicationTable applications={processedApplications} />
        </TabsContent>
      </Tabs>

      {selectedApplication && (
        <ApplicationDetailsModal
          application={selectedApplication}
          isOpen={isDetailsModalOpen}
          onClose={() => setIsDetailsModalOpen(false)}
          onApprove={() => handleStatusUpdate(selectedApplication.application_id, 'approved')}
          onReject={() => handleStatusUpdate(selectedApplication.application_id, 'rejected')}
          onWaitlist={() => handleStatusUpdate(selectedApplication.application_id, 'waitlisted')}
          isLoading={isLoading}
        />
      )}
    </div>
  );
};

export default ApplicationsPage;