import { NextResponse } from 'next/server';
import pool from '@/lib/db';
import { Graduate, Project, SocialLink } from '@/lib/types';
import { RowDataPacket } from 'mysql2';
import { uploadToCloudinary } from '@/lib/cloudinary';

export async function GET(request: Request) {
  try {
    const id = request.url.split('/').pop();

    if (!id) {
      return NextResponse.json(
        { error: 'Missing graduate ID' },
        { status: 400 }
      );
    }

    const [graduates] = await pool.query<(Graduate & RowDataPacket)[]>(
      `SELECT * FROM graduates WHERE graduate_id = ?`,
      [id]
    );
    
    if (!graduates[0]) {
      return NextResponse.json(
        { error: 'Graduate not found' },
        { status: 404 }
      );
    }

    const graduate = graduates[0];
    
    // Fetch related projects and social links
    const [projects] = await pool.query<(Project & RowDataPacket)[]>(
      'SELECT * FROM projects WHERE graduate_id = ?',
      [id]
    );
    
    const [socialLinks] = await pool.query<(SocialLink & RowDataPacket)[]>(
      'SELECT * FROM social_links WHERE graduate_id = ?',
      [id]
    );

    return NextResponse.json({
      ...graduate,
      projects,
      social_links: socialLinks
    });
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request) {
  const connection = await pool.getConnection();
  await connection.beginTransaction();

  try {
    const id = request.url.split('/').pop();

    if (!id) {
      return NextResponse.json(
        { error: 'Missing graduate ID' },
        { status: 400 }
      );
    }

    const formData = await request.formData();
    
    // Handle certificate file upload if provided
    let certificateFileUrl = undefined;
    const certificateFile = formData.get('certificate_file') as File;
    if (certificateFile) {
      certificateFileUrl = await uploadToCloudinary(certificateFile, 'certificates');
    }

    // Parse JSON strings from form data
    const graduateData = JSON.parse(formData.get('graduateData') as string);
    const projectsData = JSON.parse(formData.get('projects') as string);
    const socialLinksData = JSON.parse(formData.get('social_links') as string);

    // Update graduate
    await connection.query(
      'UPDATE graduates SET ? WHERE graduate_id = ?',
      [
        {
          ...graduateData,
          ...(certificateFileUrl && { certificate_file_url: certificateFileUrl })
        },
        id
      ]
    );

    // Delete existing projects and social links
    await connection.query('DELETE FROM projects WHERE graduate_id = ?', [id]);
    await connection.query('DELETE FROM social_links WHERE graduate_id = ?', [id]);

    // Insert updated projects
    for (const project of projectsData) {
      await connection.query(
        'INSERT INTO projects SET ?',
        { ...project, graduate_id: id }
      );
    }

    // Insert updated social links
    for (const link of socialLinksData) {
      await connection.query(
        'INSERT INTO social_links SET ?',
        { ...link, graduate_id: id }
      );
    }

    await connection.commit();
    return NextResponse.json({ success: true });
  } catch (error) {
    await connection.rollback();
    console.error(error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  } finally {
    connection.release();
  }
}

export async function DELETE(request: Request) {
  try {
    const id = request.url.split('/').pop();

    if (!id) {
      return NextResponse.json(
        { error: 'Missing graduate ID' },
        { status: 400 }
      );
    }

    const [result] = await pool.query(
      'DELETE FROM graduates WHERE graduate_id = ?',
      [id]
    );

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    if ((result as any).affectedRows === 0) {
      return NextResponse.json(
        { error: 'Graduate not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
