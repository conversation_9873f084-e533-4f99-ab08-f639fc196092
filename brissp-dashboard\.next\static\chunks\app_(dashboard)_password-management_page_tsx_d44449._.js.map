{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/app/%28dashboard%29/password-management/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { toast } from \"sonner\";\r\n\r\ninterface User {\r\n  admin_id: number;\r\n  email: string;\r\n  full_name: string;\r\n}\r\n\r\nexport default function PasswordManagement() {\r\n  const [users, setUsers] = useState<User[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [selectedUser, setSelectedUser] = useState<number | null>(null);\r\n  const [newPassword, setNewPassword] = useState(\"\");\r\n\r\n  useEffect(() => {\r\n    const fetchUsers = async () => {\r\n      try {\r\n        const response = await fetch(\"/api/admin/users\", {\r\n          credentials: \"include\",\r\n        });\r\n        \r\n        if (response.ok) {\r\n          const data = await response.json();\r\n          setUsers(data.users);\r\n        } else {\r\n          toast.error(\"Failed to fetch users\");\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching users:\", error);\r\n        toast.error(\"An error occurred while fetching users\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchUsers();\r\n  }, []);\r\n\r\n  const handlePasswordChange = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    \r\n    if (!selectedUser) {\r\n      toast.error(\"Please select a user\");\r\n      return;\r\n    }\r\n    \r\n    if (!newPassword) {\r\n      toast.error(\"Please enter a new password\");\r\n      return;\r\n    }\r\n    \r\n    try {\r\n      setLoading(true);\r\n      const response = await fetch(`/api/admin/users/${selectedUser}/password`, {\r\n        method: \"PUT\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        credentials: \"include\",\r\n        body: JSON.stringify({ password: newPassword }),\r\n      });\r\n      \r\n      if (response.ok) {\r\n        toast.success(\"Password updated successfully\");\r\n        setNewPassword(\"\");\r\n        setSelectedUser(null);\r\n      } else {\r\n        const data = await response.json();\r\n        toast.error(data.error || \"Failed to update password\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error updating password:\", error);\r\n      toast.error(\"An error occurred while updating password\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n \r\n      <div className=\"p-6\">\r\n        <h1 className=\"text-2xl font-bold mb-6\">Password Management</h1>\r\n        \r\n        {loading ? (\r\n          <div className=\"flex justify-center\">\r\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"></div>\r\n          </div>\r\n        ) : (\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n            <div className=\"bg-white p-6 rounded-lg shadow\">\r\n              <h2 className=\"text-lg font-semibold mb-4\">Select User</h2>\r\n              {users.length === 0 ? (\r\n                <p className=\"text-gray-500\">No users found</p>\r\n              ) : (\r\n                <div className=\"space-y-2 max-h-96 overflow-y-auto\">\r\n                  {users.map((user) => (\r\n                    <div \r\n                      key={user.admin_id}\r\n                      className={`p-3 border rounded-lg cursor-pointer ${\r\n                        selectedUser === user.admin_id ? \"border-blue-500 bg-blue-50\" : \"border-gray-200 hover:bg-gray-50\"\r\n                      }`}\r\n                      onClick={() => setSelectedUser(user.admin_id)}\r\n                    >\r\n                      <p className=\"font-medium\">{user.full_name}</p>\r\n                      <p className=\"text-sm text-gray-500\">{user.email}</p>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n            \r\n            <div className=\"bg-white p-6 rounded-lg shadow\">\r\n              <h2 className=\"text-lg font-semibold mb-4\">Change Password</h2>\r\n              <form onSubmit={handlePasswordChange}>\r\n                <div className=\"mb-4\">\r\n                  <label htmlFor=\"newPassword\" className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                    New Password\r\n                  </label>\r\n                  <input\r\n                    type=\"password\"\r\n                    id=\"newPassword\"\r\n                    value={newPassword}\r\n                    onChange={(e) => setNewPassword(e.target.value)}\r\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\r\n                    placeholder=\"Enter new password\"\r\n                    disabled={!selectedUser}\r\n                  />\r\n                </div>\r\n                <button\r\n                  type=\"submit\"\r\n                  disabled={!selectedUser || !newPassword || loading}\r\n                  className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                >\r\n                  Update Password\r\n                </button>\r\n              </form>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAWe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM;2DAAa;oBACjB,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM,oBAAoB;4BAC/C,aAAa;wBACf;wBAEA,IAAI,SAAS,EAAE,EAAE;4BACf,MAAM,OAAO,MAAM,SAAS,IAAI;4BAChC,SAAS,KAAK,KAAK;wBACrB,OAAO;4BACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wBACd;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,yBAAyB;wBACvC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACd,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;uCAAG,EAAE;IAEL,MAAM,uBAAuB,OAAO;QAClC,EAAE,cAAc;QAEhB,IAAI,CAAC,cAAc;YACjB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,aAAa;YAChB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,CAAC,iBAAiB,EAAE,aAAa,SAAS,CAAC,EAAE;gBACxE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;oBAAE,UAAU;gBAAY;YAC/C;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,eAAe;gBACf,gBAAgB;YAClB,OAAO;gBACL,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBAEI,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA0B;;;;;;YAEvC,wBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;qCAGjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA6B;;;;;;4BAC1C,MAAM,MAAM,KAAK,kBAChB,6LAAC;gCAAE,WAAU;0CAAgB;;;;;qDAE7B,6LAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;wCAEC,WAAW,CAAC,qCAAqC,EAC/C,iBAAiB,KAAK,QAAQ,GAAG,+BAA+B,oCAChE;wCACF,SAAS,IAAM,gBAAgB,KAAK,QAAQ;;0DAE5C,6LAAC;gDAAE,WAAU;0DAAe,KAAK,SAAS;;;;;;0DAC1C,6LAAC;gDAAE,WAAU;0DAAyB,KAAK,KAAK;;;;;;;uCAP3C,KAAK,QAAQ;;;;;;;;;;;;;;;;kCAc5B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,6LAAC;gCAAK,UAAU;;kDACd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,SAAQ;gDAAc,WAAU;0DAA+C;;;;;;0DAGtF,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;gDACV,aAAY;gDACZ,UAAU,CAAC;;;;;;;;;;;;kDAGf,6LAAC;wCACC,MAAK;wCACL,UAAU,CAAC,gBAAgB,CAAC,eAAe;wCAC3C,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAtIwB;KAAA"}}, {"offset": {"line": 257, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}