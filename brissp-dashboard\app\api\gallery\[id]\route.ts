import { NextResponse } from 'next/server';
import pool from '@/lib/db';
import { Gallery } from '@/lib/types';
import { ResultSetHeader, RowDataPacket } from 'mysql2';

export async function GET(request: Request) {
    try {
        const id = request.url.split('/').pop();

        if (!id) {
            return NextResponse.json(
                { error: 'Missing gallery ID' },
                { status: 400 }
            );
        }

        const [rows] = await pool.query<(Gallery & RowDataPacket)[]>(
            'SELECT * FROM gallery WHERE gallery_id = ? AND is_active = true',
            [id]
        );
        
        if (!rows[0]) {
            return NextResponse.json(
                { error: 'Gallery not found' },
                { status: 404 }
            );
        }

        return NextResponse.json(rows[0]);
    } catch (error) {
        console.error(error);
        return NextResponse.json(
            { error: 'Internal Server Error' },
            { status: 500 }
        );
    }
}

export async function PUT(request: Request) {
    try {
        const id = request.url.split('/').pop();

        if (!id) {
            return NextResponse.json(
                { error: 'Missing gallery ID' },
                { status: 400 }
            );
        }

        const body: Partial<Gallery> = await request.json();
        const {
            image_title,
            image_description,
            image_type,
            display_order,
            is_active
        } = body;

        const [result] = await pool.query<ResultSetHeader>(
            `UPDATE gallery
             SET image_title = COALESCE(?, image_title),
                 image_description = COALESCE(?, image_description),
                 image_type = COALESCE(?, image_type),
                 display_order = COALESCE(?, display_order),
                 is_active = COALESCE(?, is_active)
             WHERE gallery_id = ?`,
            [image_title, image_description, image_type, display_order, is_active, id]
        );

        if (result.affectedRows === 0) {
            return NextResponse.json(
                { error: 'Gallery not found' },
                { status: 404 }
            );
        }

        return NextResponse.json({ message: 'Updated successfully' });
    } catch (error) {
        console.error(error);
        return NextResponse.json(
            { error: 'Internal Server Error' },
            { status: 500 }
        );
    }
}

export async function DELETE(request: Request) {
    try {
        const id = request.url.split('/').pop();

        if (!id) {
            return NextResponse.json(
                { error: 'Missing gallery ID' },
                { status: 400 }
            );
        }

        const [result] = await pool.query<ResultSetHeader>(
            'UPDATE gallery SET is_active = false WHERE gallery_id = ?',
            [id]
        );

        if (result.affectedRows === 0) {
            return NextResponse.json(
                { error: 'Gallery not found' },
                { status: 404 }
            );
        }

        return NextResponse.json({ message: 'Deleted successfully' });
    } catch (error) {
        console.error(error);
        return NextResponse.json(
            { error: 'Internal Server Error' },
            { status: 500 }
        );
    }
}