"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import CourseForm from "@/components/CourseForm"
import { Course } from "@/lib/types"

export default function NewCoursePage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (data: Partial<Course>, imageFile?: File) => {
    setIsLoading(true)
    try {
      let imageUrl = data.image_url

      // Handle image upload if a new file is provided
      if (imageFile) {
        const formData = new FormData()
        formData.append('file', imageFile)

        const uploadResponse = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        })

        if (uploadResponse.ok) {
          const { url } = await uploadResponse.json()
          imageUrl = url
        }
      }

      // Create course with image URL
      const response = await fetch('/api/courses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          image_url: imageUrl,
        }),
      })
      
      if (response.ok) {
        router.push('/courses')
      }
    } catch (error) {
      console.error('Error creating course:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="p-6 ">
      <h1 className="text-2xl font-bold mb-6">Create New Course</h1>
      <div className="bg-white rounded-lg border p-6">
        <CourseForm onSubmit={handleSubmit} isLoading={isLoading} />
      </div>
    </div>
  )
} 