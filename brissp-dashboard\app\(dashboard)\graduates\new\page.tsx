"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import GraduateForm from "@/components/GraduateForm"
import { Graduate } from "@/lib/types"

export default function NewGraduatePage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (data: Partial<Graduate>, certificateFile: File | null | undefined) => {
    setIsLoading(true)
    try {
      let certificateUrl = "";

      // Handle certificate upload to Cloudinary if a new file is provided
      if (certificateFile) {
        const formData = new FormData();
        formData.append('file', certificateFile);
        formData.append('upload_preset', 'your_upload_preset'); // Replace with your Cloudinary upload preset

        const uploadResponse = await fetch(`https://api.cloudinary.com/v1_1/your_cloud_name/image/upload`, { // Replace with your Cloudinary cloud name
          method: 'POST',
          body: formData,
        });

        if (uploadResponse.ok) {
          const { secure_url } = await uploadResponse.json();
          certificateUrl = secure_url;
        }
      }

      // Create graduate with certificate URL
      const response = await fetch('/api/graduates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          certificate_file_url: certificateUrl,
        }),
      });

      if (response.ok) {
        router.push('/graduates');
      }
    } catch (error) {
      console.error('Error creating graduate:', error);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="p-6 ">
      <h1 className="text-2xl font-bold mb-6">Create New Graduate</h1>
      <div className="bg-white rounded-lg border p-6">
        <GraduateForm onSubmit={handleSubmit} isLoading={isLoading} />
      </div>
    </div>
  )
} 