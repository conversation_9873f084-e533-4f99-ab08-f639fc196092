"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import RefresherCourseForm from "@/components/RefresherCourseForm"
import { Course } from "@/lib/types"

export default function NewRefresherCoursePage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (data: Partial<Course>, imageFile?: File) => {
    setIsLoading(true)
    try {
      let imageUrl = data.image_url

      // Handle image upload if a new file is provided
      if (imageFile) {
        const formData = new FormData()
        formData.append('file', imageFile)

        const uploadResponse = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        })

        if (uploadResponse.ok) {
          const { url } = await uploadResponse.json()
          imageUrl = url
        }
      }

      // Create refresher course with image URL and features
      const response = await fetch('/api/courses/refresher', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          image_url: imageUrl,
        }),
      })
      
      if (response.ok) {
        router.push('/courses')
      }
    } catch (error) {
      console.error('Error creating refresher course:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="p-6 ">
      <h1 className="text-2xl font-bold mb-6">Create New Refresher Course</h1>
      <div className="bg-white rounded-lg border p-6">
        <RefresherCourseForm onSubmit={handleSubmit} isLoading={isLoading} />
      </div>
    </div>
  )
}
