{"version": 3, "sources": [], "sections": [{"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/lib/db.ts"], "sourcesContent": ["import mysql from 'mysql2/promise';\r\nimport fs from 'fs';\r\nimport path from 'path';\r\n\r\nconst pool = mysql.createPool({\r\n  host: process.env.MYSQL_HOST,\r\n  port: parseInt(process.env.MYSQL_PORT || '20423'),\r\n  database: process.env.MYSQL_DATABASE,\r\n  user: process.env.MYSQL_USER,\r\n  password: process.env.MYSQL_PASSWORD,\r\n  ssl: {\r\n    ca: fs.readFileSync(path.resolve(process.cwd(), 'aiven.pem')),\r\n    rejectUnauthorized: true\r\n  },\r\n  waitForConnections: true,\r\n  connectionLimit: 10,\r\n  queueLimit: 0\r\n});\r\n\r\nexport default pool;"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,OAAO,mIAAA,CAAA,UAAK,CAAC,UAAU,CAAC;IAC5B,MAAM,QAAQ,GAAG,CAAC,UAAU;IAC5B,MAAM,SAAS,QAAQ,GAAG,CAAC,UAAU,IAAI;IACzC,UAAU,QAAQ,GAAG,CAAC,cAAc;IACpC,MAAM,QAAQ,GAAG,CAAC,UAAU;IAC5B,UAAU,QAAQ,GAAG,CAAC,cAAc;IACpC,KAAK;QACH,IAAI,6FAAA,CAAA,UAAE,CAAC,YAAY,CAAC,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI;QAChD,oBAAoB;IACtB;IACA,oBAAoB;IACpB,iBAAiB;IACjB,YAAY;AACd;uCAEe"}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/lib/auth.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\r\nimport bcrypt from 'bcrypt';\r\nimport jwt from 'jsonwebtoken';\r\nimport { cookies } from 'next/headers';\r\nimport { NextRequest, NextResponse } from 'next/server';\r\nimport pool from './db';\r\n\r\n// Types\r\nexport interface AdminUser {\r\n  admin_id: number;\r\n  email: string;\r\n  full_name: string;\r\n  is_super_admin: boolean;\r\n}\r\n\r\n// JWT Secret - should be in environment variables\r\nconst JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-this-in-production';\r\nconst JWT_EXPIRES_IN = '24h';\r\n\r\n// Hash password\r\nexport async function hashPassword(password: string): Promise<string> {\r\n  const saltRounds = 10;\r\n  return bcrypt.hash(password, saltRounds);\r\n}\r\n\r\n// Compare password\r\nexport async function comparePassword(password: string, hashedPassword: string): Promise<boolean> {\r\n  return bcrypt.compare(password, hashedPassword);\r\n}\r\n\r\n// Generate JWT token\r\nexport function generateToken(user: AdminUser): string {\r\n  return jwt.sign(\r\n    { \r\n      id: user.admin_id,\r\n      email: user.email,\r\n      is_super_admin: user.is_super_admin \r\n    },\r\n    JWT_SECRET,\r\n    { expiresIn: JWT_EXPIRES_IN }\r\n  );\r\n}\r\n\r\n// Verify JWT token\r\nexport function verifyToken(token: string): any {\r\n  try {\r\n    return jwt.verify(token, JWT_SECRET);\r\n  } catch {\r\n    return null;\r\n  }\r\n}\r\n\r\n// Set auth cookie\r\nexport function setAuthCookie(token: string): void {\r\n  // Create a response object to set the cookie\r\n  const response = NextResponse.next();\r\n  \r\n  // Set the cookie directly on the response\r\n  response.cookies.set({\r\n    name: 'admin_token',\r\n    value: token,\r\n    httpOnly: true,\r\n    secure: process.env.NODE_ENV === 'production',\r\n    maxAge: 60 * 60 * 24, // 1 day\r\n    path: '/',\r\n    sameSite: 'lax', // Changed from 'strict' to 'lax' for better compatibility\r\n  });\r\n  \r\n  // No return since function is void\r\n  // Remove return since function is void\r\n}\r\n\r\n// Clear auth cookie\r\nexport async function clearAuthCookie(): Promise<void> {\r\n  const cookieStore = await cookies();\r\n  cookieStore.set({\r\n    name: 'admin_token',\r\n    value: '',\r\n    httpOnly: true,\r\n    secure: process.env.NODE_ENV === 'production',\r\n    maxAge: 0,\r\n    path: '/',\r\n    sameSite: 'strict',\r\n  });\r\n}\r\n\r\n// Get current admin user from token\r\nexport async function getCurrentAdmin(): Promise<AdminUser | null> {\r\n  const cookieStore = cookies();\r\n  const token = (await cookieStore).get('admin_token')?.value;\r\n  \r\n  if (!token) return null;\r\n  \r\n  const decoded = verifyToken(token);\r\n  if (!decoded) return null;\r\n  \r\n  try {\r\n    const [rows] = await pool.query(\r\n      'SELECT admin_id, email, full_name, is_super_admin FROM admin_users WHERE admin_id = ?',\r\n      [decoded.id]\r\n    );\r\n    \r\n    const users = rows as any[];\r\n    if (users.length === 0) return null;\r\n    \r\n    return users[0] as AdminUser;\r\n  } catch (error) {\r\n    console.error('Error fetching current admin:', error);\r\n    return null;\r\n  }\r\n}\r\n\r\n// Auth middleware for API routes\r\nexport async function authMiddleware(req: NextRequest) {\r\n  const token = req.cookies.get('admin_token')?.value;\r\n  \r\n  if (!token) {\r\n    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\r\n  }\r\n  \r\n  const decoded = verifyToken(token);\r\n  if (!decoded) {\r\n    return NextResponse.json({ error: 'Invalid token' }, { status: 401 });\r\n  }\r\n  \r\n  return null; // No error, proceed\r\n}\r\n\r\n// Super admin middleware\r\nexport async function superAdminMiddleware(req: NextRequest) {\r\n  const token = req.cookies.get('admin_token')?.value;\r\n  \r\n  if (!token) {\r\n    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\r\n  }\r\n  \r\n  const decoded = verifyToken(token);\r\n  if (!decoded || !decoded.is_super_admin) {\r\n    return NextResponse.json({ error: 'Forbidden: Requires super admin privileges' }, { status: 403 });\r\n  }\r\n  \r\n  return null; // No error, proceed\r\n}\r\n\r\n// Add this function if it doesn't exist already\r\n// Fix the verifyAuth function to use admin_token instead of auth_token\r\nexport async function verifyAuth(req: NextRequest) {\r\n  try {\r\n    // Get the token from the cookie\r\n    const token = req.cookies.get('admin_token')?.value;\r\n    \r\n    if (!token) {\r\n      return null;\r\n    }\r\n    \r\n    // Verify the token\r\n    const decoded = jwt.verify(token, JWT_SECRET);\r\n    \r\n    // Return the decoded user data\r\n    return decoded;\r\n  } catch (error) {\r\n    console.error('Token verification error:', error);\r\n    return null;\r\n  }\r\n}"], "names": [], "mappings": "AAAA,qDAAqD;;;;;;;;;;;;AACrD;AACA;AACA;AACA;AACA;;;;;;AAUA,kDAAkD;AAClD,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAC7C,MAAM,iBAAiB;AAGhB,eAAe,aAAa,QAAgB;IACjD,MAAM,aAAa;IACnB,OAAO,qGAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,gBAAgB,QAAgB,EAAE,cAAsB;IAC5E,OAAO,qGAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AAClC;AAGO,SAAS,cAAc,IAAe;IAC3C,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CACb;QACE,IAAI,KAAK,QAAQ;QACjB,OAAO,KAAK,KAAK;QACjB,gBAAgB,KAAK,cAAc;IACrC,GACA,YACA;QAAE,WAAW;IAAe;AAEhC;AAGO,SAAS,YAAY,KAAa;IACvC,IAAI;QACF,OAAO,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;IAC3B,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,SAAS,cAAc,KAAa;IACzC,6CAA6C;IAC7C,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI;IAElC,0CAA0C;IAC1C,SAAS,OAAO,CAAC,GAAG,CAAC;QACnB,MAAM;QACN,OAAO;QACP,UAAU;QACV,QAAQ,oDAAyB;QACjC,QAAQ,KAAK,KAAK;QAClB,MAAM;QACN,UAAU;IACZ;AAEA,mCAAmC;AACnC,uCAAuC;AACzC;AAGO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAChC,YAAY,GAAG,CAAC;QACd,MAAM;QACN,OAAO;QACP,UAAU;QACV,QAAQ,oDAAyB;QACjC,QAAQ;QACR,MAAM;QACN,UAAU;IACZ;AACF;AAGO,eAAe;IACpB,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,QAAQ,CAAC,MAAM,WAAW,EAAE,GAAG,CAAC,gBAAgB;IAEtD,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,UAAU,YAAY;IAC5B,IAAI,CAAC,SAAS,OAAO;IAErB,IAAI;QACF,MAAM,CAAC,KAAK,GAAG,MAAM,2GAAA,CAAA,UAAI,CAAC,KAAK,CAC7B,yFACA;YAAC,QAAQ,EAAE;SAAC;QAGd,MAAM,QAAQ;QACd,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;QAE/B,OAAO,KAAK,CAAC,EAAE;IACjB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;IACT;AACF;AAGO,eAAe,eAAe,GAAgB;IACnD,MAAM,QAAQ,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB;IAE9C,IAAI,CAAC,OAAO;QACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAe,GAAG;YAAE,QAAQ;QAAI;IACpE;IAEA,MAAM,UAAU,YAAY;IAC5B,IAAI,CAAC,SAAS;QACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAgB,GAAG;YAAE,QAAQ;QAAI;IACrE;IAEA,OAAO,MAAM,oBAAoB;AACnC;AAGO,eAAe,qBAAqB,GAAgB;IACzD,MAAM,QAAQ,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB;IAE9C,IAAI,CAAC,OAAO;QACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAe,GAAG;YAAE,QAAQ;QAAI;IACpE;IAEA,MAAM,UAAU,YAAY;IAC5B,IAAI,CAAC,WAAW,CAAC,QAAQ,cAAc,EAAE;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAA6C,GAAG;YAAE,QAAQ;QAAI;IAClG;IAEA,OAAO,MAAM,oBAAoB;AACnC;AAIO,eAAe,WAAW,GAAgB;IAC/C,IAAI;QACF,gCAAgC;QAChC,MAAM,QAAQ,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB;QAE9C,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,mBAAmB;QACnB,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;QAElC,+BAA+B;QAC/B,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF"}}, {"offset": {"line": 353, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 359, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/app/api/admin/users/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\r\nimport pool from '@/lib/db';\r\nimport { authMiddleware } from '@/lib/auth';\r\n\r\nexport async function GET(req: NextRequest) {\r\n  try {\r\n    // Check if user is authenticated\r\n    const authError = await authMiddleware(req);\r\n    if (authError) return authError;\r\n    \r\n    // Get all admin users\r\n    const [rows] = await pool.query(\r\n      'SELECT admin_id, email, full_name FROM admin_users ORDER BY full_name'\r\n    );\r\n    \r\n    return NextResponse.json({ users: rows });\r\n  } catch (error) {\r\n    console.error('Get users error:', error);\r\n    return NextResponse.json(\r\n      { error: 'An error occurred while fetching users' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,IAAI,GAAgB;IACxC,IAAI;QACF,iCAAiC;QACjC,MAAM,YAAY,MAAM,CAAA,GAAA,6GAAA,CAAA,iBAAc,AAAD,EAAE;QACvC,IAAI,WAAW,OAAO;QAEtB,sBAAsB;QACtB,MAAM,CAAC,KAAK,GAAG,MAAM,2GAAA,CAAA,UAAI,CAAC,KAAK,CAC7B;QAGF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAK;IACzC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyC,GAClD;YAAE,QAAQ;QAAI;IAElB;AACF"}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}