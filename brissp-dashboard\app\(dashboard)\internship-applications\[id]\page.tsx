"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft, User, GraduationCap, FileText, Calendar, Settings, Save, Briefcase, Globe, Code } from "lucide-react"

interface InternshipApplication {
  application_id: number;
  internship_id?: number;
  applicant_name: string;
  email: string;
  phone: string;
  university: string;
  course_of_study: string;
  year_of_study: string;
  gpa?: string;
  cv_url?: string;
  cover_letter?: string;
  portfolio_url?: string;
  linkedin_url?: string;
  github_url?: string;
  availability_start?: string;
  preferred_duration?: string;
  motivation?: string;
  relevant_experience?: string;
  technical_skills?: string;
  soft_skills?: string;
  preferred_industry?: string;
  internship_type_preference?: 'paid' | 'unpaid' | 'both';
  status: 'pending' | 'reviewed' | 'shortlisted' | 'accepted' | 'rejected';
  application_date: string;
  reviewed_by?: number;
  review_date?: string;
  review_notes?: string;
}

export default function InternshipApplicationDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const [application, setApplication] = useState<InternshipApplication | null>(null)
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)
  const [status, setStatus] = useState("")
  const [reviewNotes, setReviewNotes] = useState("")

  useEffect(() => {
    if (params.id) {
      fetchApplication()
    }
  }, [params.id])

  const fetchApplication = async () => {
    try {
      const response = await fetch(`/api/internship-applications/${params.id}`)
      const data = await response.json()
      
      if (response.ok) {
        setApplication(data.application)
        setStatus(data.application.status)
        setReviewNotes(data.application.review_notes || "")
      }
    } catch (error) {
      console.error('Error fetching application:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleUpdate = async () => {
    if (!application) return

    setUpdating(true)
    try {
      const response = await fetch(`/api/internship-applications/${application.application_id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status,
          review_notes: reviewNotes,
        }),
      })

      if (response.ok) {
        fetchApplication()
        alert('Application updated successfully!')
      }
    } catch (error) {
      console.error('Error updating application:', error)
      alert('Error updating application')
    } finally {
      setUpdating(false)
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { variant: "secondary" as const, label: "Pending", className: "bg-yellow-100 text-yellow-800" },
      reviewed: { variant: "outline" as const, label: "Under Review", className: "bg-blue-100 text-blue-800" },
      shortlisted: { variant: "default" as const, label: "Shortlisted", className: "bg-purple-100 text-purple-800" },
      accepted: { variant: "default" as const, label: "Accepted", className: "bg-green-100 text-green-800" },
      rejected: { variant: "destructive" as const, label: "Rejected", className: "bg-red-100 text-red-800" },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return (
      <Badge className={config.className}>
        {config.label}
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="text-center py-8">Loading application details...</div>
      </div>
    )
  }

  if (!application) {
    return (
      <div className="p-6">
        <div className="text-center py-8">Application not found</div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Internship Application Details</h1>
            <p className="text-gray-600">Application #{application.application_id}</p>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          {getStatusBadge(application.status)}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Applicant Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="w-5 h-5" />
                <span>Applicant Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Full Name</label>
                  <p className="text-sm">{application.applicant_name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Email</label>
                  <p className="text-sm">{application.email}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Phone</label>
                  <p className="text-sm">{application.phone}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">GPA</label>
                  <p className="text-sm">{application.gpa || 'Not provided'}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Academic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <GraduationCap className="w-5 h-5" />
                <span>Academic Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">University</label>
                  <p className="text-sm">{application.university}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Course of Study</label>
                  <p className="text-sm">{application.course_of_study}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Year of Study</label>
                  <p className="text-sm">{application.year_of_study}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Internship Preferences */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Briefcase className="w-5 h-5" />
                <span>Internship Preferences</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Type Preference</label>
                  <p className="text-sm capitalize">{application.internship_type_preference || 'Not specified'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Preferred Industry</label>
                  <p className="text-sm">{application.preferred_industry || 'Not specified'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Availability Start</label>
                  <p className="text-sm">{application.availability_start ? new Date(application.availability_start).toLocaleDateString() : 'Not specified'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Preferred Duration</label>
                  <p className="text-sm">{application.preferred_duration || 'Not specified'}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Skills and Experience */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Code className="w-5 h-5" />
                <span>Skills and Experience</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {application.technical_skills && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Technical Skills</label>
                  <p className="text-sm mt-1">{application.technical_skills}</p>
                </div>
              )}
              {application.soft_skills && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Soft Skills</label>
                  <p className="text-sm mt-1">{application.soft_skills}</p>
                </div>
              )}
              {application.relevant_experience && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Relevant Experience</label>
                  <p className="text-sm mt-1">{application.relevant_experience}</p>
                </div>
              )}
              {application.motivation && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Motivation</label>
                  <p className="text-sm mt-1">{application.motivation}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Documents and Links */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Globe className="w-5 h-5" />
                <span>Documents and Links</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {application.cv_url && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">CV/Resume</label>
                    <p className="text-sm">
                      <a href={application.cv_url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                        View CV
                      </a>
                    </p>
                  </div>
                )}
                {application.portfolio_url && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Portfolio</label>
                    <p className="text-sm">
                      <a href={application.portfolio_url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                        View Portfolio
                      </a>
                    </p>
                  </div>
                )}
                {application.linkedin_url && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">LinkedIn</label>
                    <p className="text-sm">
                      <a href={application.linkedin_url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                        View LinkedIn
                      </a>
                    </p>
                  </div>
                )}
                {application.github_url && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">GitHub</label>
                    <p className="text-sm">
                      <a href={application.github_url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                        View GitHub
                      </a>
                    </p>
                  </div>
                )}
              </div>
              {application.cover_letter && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Cover Letter</label>
                  <p className="text-sm mt-1 p-3 bg-gray-50 rounded">{application.cover_letter}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Application Management */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="w-5 h-5" />
                <span>Application Management</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-700">Status</label>
                <Select value={status} onValueChange={setStatus}>
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="reviewed">Under Review</SelectItem>
                    <SelectItem value="shortlisted">Shortlisted</SelectItem>
                    <SelectItem value="accepted">Accepted</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">Review Notes</label>
                <Textarea
                  value={reviewNotes}
                  onChange={(e) => setReviewNotes(e.target.value)}
                  placeholder="Add review notes..."
                  className="mt-1"
                  rows={4}
                />
              </div>

              <Button
                onClick={handleUpdate}
                disabled={updating}
                className="w-full"
              >
                <Save className="w-4 h-4 mr-2" />
                {updating ? 'Updating...' : 'Update Application'}
              </Button>
            </CardContent>
          </Card>

          {/* Application Timeline */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Calendar className="w-5 h-5" />
                <span>Application Timeline</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Applied Date</label>
                <p className="text-sm">{new Date(application.application_date).toLocaleDateString()}</p>
              </div>
              {application.review_date && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Review Date</label>
                  <p className="text-sm">{new Date(application.review_date).toLocaleDateString()}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
