import { NextResponse } from 'next/server';
import pool from '@/lib/db';
import { RowDataPacket } from 'mysql2';

export async function GET() {
  try {
    // First check if table exists
    const [tableExists] = await pool.query<RowDataPacket[]>(
      `SELECT COUNT(*) as count FROM information_schema.tables
       WHERE table_schema = DATABASE() AND table_name = 'internship_applications'`
    );

    if (tableExists[0].count === 0) {
      // Check what tables do exist
      const [allTables] = await pool.query<RowDataPacket[]>(
        `SELECT table_name FROM information_schema.tables
         WHERE table_schema = DATABASE()
         ORDER BY table_name`
      );

      return NextResponse.json({
        error: 'Table internship_applications does not exist',
        availableTables: allTables.map(t => t.table_name),
        message: 'Table not found'
      });
    }

    // Get table structure
    const [tableStructure] = await pool.query<RowDataPacket[]>(
      'DESCRIBE internship_applications'
    );

    // Get all column names
    const [allColumns] = await pool.query<RowDataPacket[]>(
      `SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
       FROM information_schema.COLUMNS
       WHERE TABLE_SCHEMA = DATABASE()
       AND TABLE_NAME = 'internship_applications'
       ORDER BY ORDINAL_POSITION`
    );

    // Try to get a sample record to see actual data
    let sampleRecord = null;
    let recordCount = 0;
    try {
      const [countResult] = await pool.query<RowDataPacket[]>(
        'SELECT COUNT(*) as count FROM internship_applications'
      );
      recordCount = countResult[0].count;

      if (recordCount > 0) {
        const [sample] = await pool.query<RowDataPacket[]>(
          'SELECT * FROM internship_applications LIMIT 1'
        );
        sampleRecord = sample[0] || null;
      }
    } catch (error) {
      console.log('Error getting sample records:', error);
    }

    return NextResponse.json({
      tableExists: true,
      recordCount,
      tableStructure,
      allColumns,
      sampleRecord,
      message: 'Table structure retrieved successfully'
    });
  } catch (error) {
    console.error('Database debug error:', error);
    return NextResponse.json({
      error: 'Failed to get table structure',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
