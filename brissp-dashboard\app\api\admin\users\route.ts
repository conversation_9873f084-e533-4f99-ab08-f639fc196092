import { NextRequest, NextResponse } from 'next/server';
import pool from '@/lib/db';
import { authMiddleware } from '@/lib/auth';

export async function GET(req: NextRequest) {
  try {
    // Check if user is authenticated
    const authError = await authMiddleware(req);
    if (authError) return authError;
    
    // Get all admin users
    const [rows] = await pool.query(
      'SELECT admin_id, email, full_name FROM admin_users ORDER BY full_name'
    );
    
    return NextResponse.json({ users: rows });
  } catch (error) {
    console.error('Get users error:', error);
    return NextResponse.json(
      { error: 'An error occurred while fetching users' },
      { status: 500 }
    );
  }
}