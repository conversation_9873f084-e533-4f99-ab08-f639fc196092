"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Plus, Pencil, Trash2 } from "lucide-react"
import Link from "next/link"
import { Notice } from "@/lib/types"

const NoticeBoard = () => {
  const [notices, setNotices] = useState<Notice[]>([])

  useEffect(() => {
    fetchNotices()
  }, [])

  const fetchNotices = async () => {
    try {
      const response = await fetch('/api/notices')
      const data = await response.json()
      setNotices(data)
    } catch (error) {
      console.error('Error fetching notices:', error)
    }
  }

  const handleDelete = async (noticeId: number) => {
    if (confirm('Are you sure you want to delete this notice?')) {
      try {
        const response = await fetch(`/api/notices/${noticeId}`, {
          method: 'DELETE',
        })
        if (response.ok) {
          fetchNotices()
        }
      } catch (error) {
        console.error('Error deleting notice:', error)
      }
    }
  }

  return (
    <div className="p-6 ">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Notice Board Management</h1>
        <Link href="/notice-board/new">
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            Add New Notice
          </Button>
        </Link>
      </div>

      <div className="bg-white rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Title</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {notices.map((notice) => (
              <TableRow key={notice.notice_id}>
                <TableCell>{notice.title}</TableCell>
                <TableCell>{notice.description}</TableCell>
                <TableCell className="flex space-x-2">
                  <Link href={`/notice-board/${notice.notice_id}/edit`}>
                    <Button variant="outline" size="sm">
                      <Pencil className="w-4 h-4" />
                    </Button>
                  </Link>
                  <Button 
                    variant="destructive" 
                    size="sm"
                    onClick={() => handleDelete(notice.notice_id!)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}

export default NoticeBoard