"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Plus, Pencil, Trash2, Download } from "lucide-react"
import Link from "next/link"
import { DownloadableFile } from "@/lib/types"

export default function DownloadableFilesPage() {
  const [files, setFiles] = useState<DownloadableFile[]>([])

  useEffect(() => {
    fetchFiles()
  }, [])

  const fetchFiles = async () => {
    try {
      const response = await fetch('/api/downloadables')
      const data = await response.json()
      setFiles(data)
    } catch (error) {
      console.error('Error fetching downloadable files:', error)
    }
  }

  const handleDelete = async (fileId: number) => {
    if (confirm('Are you sure you want to delete this file?')) {
      try {
        const response = await fetch(`/api/downloadables/${fileId}`, {
          method: 'DELETE',
        })
        if (response.ok) {
          fetchFiles()
        }
      } catch (error) {
        console.error('Error deleting file:', error)
      }
    }
  }

  // Function to get file extension from URL
  const getFileType = (url: string) => {
    const extension = url.split('.').pop()?.toLowerCase() || ''
    return extension
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Resource File Management</h1>
        <Link href="/downloadables/new">
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            Add New File
          </Button>
        </Link>
      </div>

      <div className="bg-white rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>File Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Course</TableHead>
              <TableHead>Upload Date</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {files.map((file) => (
              <TableRow key={file.file_id}>
                <TableCell>{file.file_name}</TableCell>
                <TableCell>{getFileType(file.file_url)}</TableCell>
                <TableCell>{file.course_title || 'N/A'}</TableCell>
                <TableCell>{new Date(file.upload_date).toLocaleDateString()}</TableCell>
                <TableCell className="flex space-x-2">
                  <a href={file.file_url} target="_blank" rel="noopener noreferrer">
                    <Button variant="outline" size="sm">
                      <Download className="w-4 h-4" />
                    </Button>
                  </a>
                  <Link href={`/downloadables/${file.file_id}/edit`}>
                    <Button variant="outline" size="sm">
                      <Pencil className="w-4 h-4" />
                    </Button>
                  </Link>
                  <Button 
                    variant="destructive" 
                    size="sm"
                    onClick={() => handleDelete(file.file_id)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}