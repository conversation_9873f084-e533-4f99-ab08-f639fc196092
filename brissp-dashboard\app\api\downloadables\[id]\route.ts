import { NextResponse } from 'next/server';
import pool from '@/lib/db';
import { DownloadableFile } from '@/lib/types';
import { ResultSetHeader, RowDataPacket } from 'mysql2';

// GET specific file by ID
export async function GET(request: Request) {
  try {
    const id = request.url.split('/').pop();

    if (!id) {
      return NextResponse.json(
        { error: 'Missing file ID' },
        { status: 400 }
      );
    }

    const [rows] = await pool.query<(DownloadableFile & RowDataPacket)[]>(
      `SELECT f.*, c.title as course_title
       FROM downloadable_files f
       LEFT JOIN courses c ON f.course_id = c.course_id
       WHERE f.file_id = ?`,
      [id]
    );
    
    if (!rows[0]) {
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(rows[0]);
  } catch (error) {
    console.error('Error fetching file:', error);
    return NextResponse.json(
      { error: 'Failed to fetch file' },
      { status: 500 }
    );
  }
}

// PUT update file
export async function PUT(request: Request) {
  try {
    const id = request.url.split('/').pop();

    if (!id) {
      return NextResponse.json(
        { error: 'Missing file ID' },
        { status: 400 }
      );
    }

    const data = await request.json();
    const { file_name, file_url, description, course_id, file_type, file_size } = data;

    if (!file_name || !file_url) {
      return NextResponse.json(
        { error: 'File name and URL are required' },
        { status: 400 }
      );
    }

    const [result] = await pool.query<ResultSetHeader>(
      `UPDATE downloadable_files SET
       file_name = ?,
       file_url = ?,
       description = ?,
       course_id = ?,
       file_type = COALESCE(?, file_type),
       file_size = COALESCE(?, file_size)
       WHERE file_id = ?`,
      [file_name, file_url, description || null, course_id || null, file_type, file_size, id]
    );
    
    if (result.affectedRows === 0) {
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ id, ...data });
  } catch (error) {
    console.error('Error updating file:', error);
    return NextResponse.json(
      { error: 'Failed to update file' },
      { status: 500 }
    );
  }
}

// DELETE file
export async function DELETE(request: Request) {
  try {
    const id = request.url.split('/').pop();

    if (!id) {
      return NextResponse.json(
        { error: 'Missing file ID' },
        { status: 400 }
      );
    }
    
    const [result] = await pool.query<ResultSetHeader>(
      'DELETE FROM downloadable_files WHERE file_id = ?',
      [id]
    );
    
    if (result.affectedRows === 0) {
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ message: 'File deleted successfully' });
  } catch (error) {
    console.error('Error deleting file:', error);
    return NextResponse.json(
      { error: 'Failed to delete file' },
      { status: 500 }
    );
  }
}