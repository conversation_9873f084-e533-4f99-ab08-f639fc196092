/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, Di<PERSON>Header, <PERSON>alogTitle, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";

interface InternshipApplicationDetailsProps {
  application: any;
  isOpen: boolean;
  onClose: () => void;
  onApprove: () => void;
  onReject: () => void;
  onWithdraw: () => void;
  isLoading: boolean;
}

export function InternshipApplicationDetailsModal({
  application,
  isOpen,
  onClose,
  onApprove,
  onReject,
  onWithdraw,
  isLoading
}: InternshipApplicationDetailsProps) {
  if (!application) return null;

  const getStatusBadge = (status: string) => {
    const statusStyles: Record<string, string> = {
      pending: "bg-yellow-100 text-yellow-800",
      reviewed: "bg-blue-100 text-blue-800",
      shortlisted: "bg-purple-100 text-purple-800",
      accepted: "bg-green-100 text-green-800",
      rejected: "bg-red-100 text-red-800",
    };
    return (
      <Badge className={statusStyles[status] || "bg-gray-100 text-gray-800"}>
        {status.charAt(0).toUpperCase() + status.slice(1).replace(/-/g, ' ')}
      </Badge>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Internship Application Details</DialogTitle>
        </DialogHeader>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 py-4">
          {/* Personal Information */}
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold border-b pb-2">Personal Information</h3>
              <div className="space-y-2 mt-2">
                <p className="text-sm"><span className="font-medium">Name:</span> {application.applicant_name}</p>
                <p className="text-sm"><span className="font-medium">Email:</span> {application.email}</p>
                <p className="text-sm"><span className="font-medium">Phone:</span> {application.phone}</p>
                <p className="text-sm"><span className="font-medium">University:</span> {application.university}</p>
                <p className="text-sm"><span className="font-medium">Course of Study:</span> {application.course_of_study}</p>
                <p className="text-sm"><span className="font-medium">Year of Study:</span> {application.year_of_study}</p>
                {application.gpa && (
                  <p className="text-sm"><span className="font-medium">GPA:</span> {application.gpa}</p>
                )}
              </div>
            </div>

            {/* Experience & Motivation */}
            <div>
              <h3 className="text-lg font-semibold border-b pb-2">Experience & Motivation</h3>
              <div className="space-y-2 mt-2">
                {application.relevant_experience && (
                  <div>
                    <p className="text-sm font-medium">Relevant Experience:</p>
                    <p className="text-sm text-gray-600 p-2 bg-gray-50 rounded">{application.relevant_experience}</p>
                  </div>
                )}
                {application.motivation && (
                  <div>
                    <p className="text-sm font-medium">Motivation:</p>
                    <p className="text-sm text-gray-600 p-2 bg-gray-50 rounded">{application.motivation}</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Internship Preferences & Skills */}
          <div className="space-y-4">
            {/* Internship Preferences */}
            <div>
              <h3 className="text-lg font-semibold border-b pb-2">Internship Preferences</h3>
              <div className="space-y-2 mt-2">
                {application.internship_type_preference && (
                  <p className="text-sm"><span className="font-medium">Type Preference:</span> {application.internship_type_preference}</p>
                )}
                {application.preferred_duration && (
                  <p className="text-sm"><span className="font-medium">Duration:</span> {application.preferred_duration}</p>
                )}
                {application.availability_start && (
                  <p className="text-sm"><span className="font-medium">Available From:</span> {format(new Date(application.availability_start), 'dd/MM/yyyy')}</p>
                )}
                {application.preferred_industry && (
                  <p className="text-sm"><span className="font-medium">Preferred Industry:</span> {application.preferred_industry}</p>
                )}
              </div>
            </div>

            {/* Skills */}
            <div>
              <h3 className="text-lg font-semibold border-b pb-2">Skills</h3>
              <div className="space-y-2 mt-2">
                {application.technical_skills && (
                  <div>
                    <p className="text-sm font-medium">Technical Skills:</p>
                    <p className="text-sm text-gray-600 p-2 bg-gray-50 rounded">{application.technical_skills}</p>
                  </div>
                )}
                {application.soft_skills && (
                  <div>
                    <p className="text-sm font-medium">Soft Skills:</p>
                    <p className="text-sm text-gray-600 p-2 bg-gray-50 rounded">{application.soft_skills}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Links & Documents */}
            <div>
              <h3 className="text-lg font-semibold border-b pb-2">Links & Documents</h3>
              <div className="space-y-2 mt-2">
                {application.portfolio_url && (
                  <p className="text-sm">
                    <span className="font-medium">Portfolio:</span>
                    <a href={application.portfolio_url} className="text-blue-600 underline ml-1" target="_blank" rel="noopener noreferrer">View</a>
                  </p>
                )}
                {application.github_url && (
                  <p className="text-sm">
                    <span className="font-medium">GitHub:</span>
                    <a href={application.github_url} className="text-blue-600 underline ml-1" target="_blank" rel="noopener noreferrer">View</a>
                  </p>
                )}
                {application.linkedin_url && (
                  <p className="text-sm">
                    <span className="font-medium">LinkedIn:</span>
                    <a href={application.linkedin_url} className="text-blue-600 underline ml-1" target="_blank" rel="noopener noreferrer">View</a>
                  </p>
                )}
                {application.cv_url && (
                  <p className="text-sm">
                    <span className="font-medium">CV:</span>
                    <a href={application.cv_url} className="text-blue-600 underline ml-1" target="_blank" rel="noopener noreferrer">Download</a>
                  </p>
                )}
              </div>
            </div>

            {/* Cover Letter */}
            {application.cover_letter && (
              <div>
                <h3 className="text-lg font-semibold border-b pb-2">Cover Letter</h3>
                <div className="space-y-2 mt-2">
                  <p className="text-sm text-gray-600 p-2 bg-gray-50 rounded">{application.cover_letter}</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Application Status */}
        <div className="border-t pt-4">
          <h3 className="text-lg font-semibold mb-3">Application Status</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">Current Status:</span>
                {getStatusBadge(application.status)}
              </div>
              <p className="text-sm"><span className="font-medium">Application Date:</span> {format(new Date(application.application_date), 'dd/MM/yyyy')}</p>
              {application.review_date && (
                <p className="text-sm"><span className="font-medium">Review Date:</span> {format(new Date(application.review_date), 'dd/MM/yyyy')}</p>
              )}
            </div>
            <div className="space-y-2">
              {application.review_notes && (
                <div>
                  <p className="text-sm font-medium">Review Notes:</p>
                  <p className="text-sm p-2 bg-gray-50 rounded">{application.review_notes}</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {application.status === 'pending' && (
          <DialogFooter className="flex space-x-2">
            <Button
              onClick={onApprove}
              disabled={isLoading}
              className="bg-green-600 hover:bg-green-700"
            >
              Accept
            </Button>
            <Button
              onClick={onReject}
              disabled={isLoading}
              variant="destructive"
            >
              Reject
            </Button>
            <Button
              onClick={onWithdraw}
              disabled={isLoading}
              variant="outline"
            >
              Mark Reviewed
            </Button>
            <Button
              onClick={onClose}
              disabled={isLoading}
              variant="outline"
            >
              Close
            </Button>
          </DialogFooter>
        )}
        {application.status !== 'pending' && (
          <DialogFooter>
            <Button
              onClick={onClose}
              variant="outline"
            >
              Close
            </Button>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
} 