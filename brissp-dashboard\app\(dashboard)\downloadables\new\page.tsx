"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import FileForm from "@/components/FileForm"
import { DownloadableFile } from "@/lib/types"

export default function NewFilePage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (data: Partial<DownloadableFile>, fileObject?: File) => {
    if (!fileObject) {
      alert('Please select a file to upload');
      return;
    }

    setIsLoading(true)
    try {
      // Upload file to Cloudinary
      const formData = new FormData()
      formData.append('file', fileObject)
      formData.append('folder', 'downloadable_files')

      const uploadResponse = await fetch('/api/upload/cloud', {
        method: 'POST',
        body: formData,
      })

      if (!uploadResponse.ok) {
        throw new Error('File upload failed');
      }

      const { url } = await uploadResponse.json()

      // Create downloadable file record
      const response = await fetch('/api/downloadables', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          file_url: url,
          file_size: fileObject.size,
          file_type: fileObject.type,
        }),
      })
      
      if (response.ok) {
        router.push('/downloadables')
      } else {
        throw new Error('Failed to create file record');
      }
    } catch (error) {
      console.error('Error creating downloadable file:', error)
      alert('Error saving file. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="p-6 ">
      <h1 className="text-2xl font-bold mb-6">Add New Resource File</h1>
      <div className="bg-white rounded-lg border p-6">
        <FileForm onSubmit={handleSubmit} isLoading={isLoading} />
      </div>
    </div>
  )
}