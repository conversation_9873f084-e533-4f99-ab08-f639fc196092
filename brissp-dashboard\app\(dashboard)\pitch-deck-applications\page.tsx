"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Eye,  Trash2, Search,  Presentation, TrendingUp, Clock, CheckCircle } from "lucide-react"
import Link from "next/link"
import { PitchDeckApplication } from "@/app/api/pitch-deck-applications/route"

export default function PitchDeckApplicationsPage() {
  const [applications, setApplications] = useState<PitchDeckApplication[]>([])
  const [loading, setLoading] = useState(true)
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [searchTerm, setSearchTerm] = useState("")
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  })
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    inProgress: 0,
    completed: 0
  })

  useEffect(() => {
    fetchApplications()
    fetchStats()
  }, [pagination.page, statusFilter])

  const fetchApplications = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
      })

      console.log('Fetching applications with statusFilter:', statusFilter)

      if (statusFilter && statusFilter !== 'all') {
        params.append('status', statusFilter)
        console.log('Adding status filter to API call:', statusFilter)
      } else {
        console.log('Not adding status filter (showing all)')
      }

      const apiUrl = `/api/pitch-deck-applications?${params}`
      console.log('API URL:', apiUrl)

      const response = await fetch(apiUrl)
      const data = await response.json()

      console.log('Pitch Deck Applications API Response:', data)
      console.log('Applications received:', data.applications?.length || 0)

      if (data.applications && data.applications.length > 0) {
        console.log('Sample application data:', data.applications[0])
      }

      setApplications(data.applications || [])
      setPagination(prev => ({
        ...prev,
        total: data.pagination?.total || 0,
        totalPages: data.pagination?.totalPages || 0
      }))
    } catch (error) {
      console.error('Error fetching applications:', error)
      setApplications([])
      alert('Error fetching applications. Please check the console for details.')
    } finally {
      setLoading(false)
    }
  }

  const fetchStats = async () => {
    try {
      // Fetch stats for different statuses
      const [totalRes, pendingRes, inProgressRes, completedRes] = await Promise.all([
        fetch('/api/pitch-deck-applications'),
        fetch('/api/pitch-deck-applications?status=pending'),
        fetch('/api/pitch-deck-applications?status=in-progress'),
        fetch('/api/pitch-deck-applications?status=completed')
      ])

      const [total, pending, inProgress, completed] = await Promise.all([
        totalRes.json(),
        pendingRes.json(),
        inProgressRes.json(),
        completedRes.json()
      ])

      setStats({
        total: total.pagination?.total || 0,
        pending: pending.pagination?.total || 0,
        inProgress: inProgress.pagination?.total || 0,
        completed: completed.pagination?.total || 0
      })
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  const handleDelete = async (applicationId: number) => {
    if (confirm('Are you sure you want to delete this application?')) {
      try {
        const response = await fetch(`/api/pitch-deck-applications/${applicationId}`, {
          method: 'DELETE',
        })
        if (response.ok) {
          fetchApplications()
          fetchStats()
        }
      } catch (error) {
        console.error('Error deleting application:', error)
      }
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { variant: "secondary" as const, label: "Pending" },
      reviewed: { variant: "outline" as const, label: "Reviewed" },
      accepted: { variant: "default" as const, label: "Accepted" },
      rejected: { variant: "destructive" as const, label: "Rejected" },
      "in-progress": { variant: "default" as const, label: "In Progress" },
      completed: { variant: "default" as const, label: "Completed" }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  const getFundingStageBadge = (stage: string) => {
    const stageColors = {
      "pre-seed": "bg-blue-100 text-blue-800",
      "seed": "bg-green-100 text-green-800",
      "series-a": "bg-purple-100 text-purple-800",
      "series-b": "bg-orange-100 text-orange-800",
      "later-stage": "bg-red-100 text-red-800"
    }
    
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${stageColors[stage as keyof typeof stageColors] || 'bg-gray-100 text-gray-800'}`}>
        {stage.replace('-', ' ').toUpperCase()}
      </span>
    )
  }

  const filteredApplications = applications.filter(app => {
  

    // Status filter check
    const statusMatch = statusFilter === 'all' || app.status === statusFilter;

    // Search term check
    const searchMatch = !searchTerm ||
      (app.applicant_name && app.applicant_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (app.email && app.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (app.company_name && app.company_name.toLowerCase().includes(searchTerm.toLowerCase()));

    const result = statusMatch && searchMatch;

    console.log('Filter result:', {
      statusMatch,
      searchMatch,
      finalResult: result
    });

    return result;
  })

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Pitch Deck Applications</h1>
          <p className="text-gray-600">Manage startup pitch deck development applications</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Applications</CardTitle>
            <Presentation className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pending}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.inProgress}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.completed}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search by name, email, or company..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="reviewed">Reviewed</SelectItem>
                <SelectItem value="accepted">Accepted</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
                <SelectItem value="in-progress">In Progress</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>



      {/* Applications Table */}
      <Card>
        <CardHeader>
          <CardTitle>Applications ({filteredApplications.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">Loading applications...</div>
          ) : applications.length === 0 ? (
            <div className="text-center py-8">
              <p>No pitch deck applications found.</p>
              <p className="text-sm text-gray-500 mt-2">
                Make sure the database table exists and contains data.
              </p>
            </div>
          ) : filteredApplications.length === 0 ? (
            <div className="text-center py-8">
              <p>No applications match your current filters.</p>
              <p className="text-sm text-gray-500 mt-2">
                Try adjusting your search terms or filters.
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Applicant</TableHead>
                  <TableHead>Company</TableHead>
                  <TableHead>Funding Stage</TableHead>
                  <TableHead>Budget Range</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Applied Date</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredApplications.map((application) => (
                  <TableRow key={application.application_id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{application.applicant_name}</div>
                        <div className="text-sm text-gray-500">{application.email}</div>
                      </div>
                    </TableCell>
                    <TableCell>{application.company_name || 'N/A'}</TableCell>
                    <TableCell>{getFundingStageBadge(application.funding_stage)}</TableCell>
                    <TableCell>
                      <span className="capitalize">{application.budget_range}</span>
                    </TableCell>
                    <TableCell>{getStatusBadge(application.status)}</TableCell>
                    <TableCell>{new Date(application.application_date).toLocaleDateString()}</TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Link href={`/pitch-deck-applications/${application.application_id}`}>
                          <Button variant="outline" size="sm">
                            <Eye className="w-4 h-4" />
                          </Button>
                        </Link>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(application.application_id)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex justify-center space-x-2">
          <Button
            variant="outline"
            onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
            disabled={pagination.page === 1}
          >
            Previous
          </Button>
          <span className="flex items-center px-4">
            Page {pagination.page} of {pagination.totalPages}
          </span>
          <Button
            variant="outline"
            onClick={() => setPagination(prev => ({ ...prev, page: Math.min(prev.totalPages, prev.page + 1) }))}
            disabled={pagination.page === pagination.totalPages}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  )
}
