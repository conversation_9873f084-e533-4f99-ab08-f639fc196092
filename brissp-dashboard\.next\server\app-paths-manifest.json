{"/(root)/page": "app/(root)/page.js", "/api/admin/auth/login/route": "app/api/admin/auth/login/route.js", "/(dashboard)/panel/page": "app/(dashboard)/panel/page.js", "/api/admin/auth/check/route": "app/api/admin/auth/check/route.js", "/api/dashboard/stats/route": "app/api/dashboard/stats/route.js", "/(dashboard)/internship-applications/page": "app/(dashboard)/internship-applications/page.js", "/api/internship-applications/route": "app/api/internship-applications/route.js", "/(dashboard)/fyp-applications/page": "app/(dashboard)/fyp-applications/page.js", "/api/fyp-applications/route": "app/api/fyp-applications/route.js", "/_not-found/page": "app/_not-found/page.js", "/(dashboard)/pitch-deck-applications/page": "app/(dashboard)/pitch-deck-applications/page.js", "/api/pitch-deck-applications/route": "app/api/pitch-deck-applications/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/(dashboard)/applications/page": "app/(dashboard)/applications/page.js", "/api/applications/route": "app/api/applications/route.js", "/(dashboard)/courses/page": "app/(dashboard)/courses/page.js", "/(dashboard)/downloadables/page": "app/(dashboard)/downloadables/page.js", "/(dashboard)/results/page": "app/(dashboard)/results/page.js", "/api/courses/route": "app/api/courses/route.js", "/api/enrollments/route": "app/api/enrollments/route.js", "/api/results/route": "app/api/results/route.js", "/(dashboard)/password-management/page": "app/(dashboard)/password-management/page.js", "/api/admin/users/route": "app/api/admin/users/route.js"}