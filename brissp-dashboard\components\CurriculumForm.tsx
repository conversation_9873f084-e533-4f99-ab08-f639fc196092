import React, { useState, useEffect } from 'react';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Curriculum, Course } from "@/lib/types";

interface CurriculumFormProps {
  initialData?: Curriculum;
  onSubmit: (data: Partial<Curriculum>) => Promise<void>;
  isLoading?: boolean;
}

const CurriculumForm: React.FC<CurriculumFormProps> = ({ initialData, onSubmit, isLoading }) => {
  const [courses, setCourses] = useState<Course[]>([]);
  const [formData, setFormData] = useState({
    course_id: initialData?.course_id || 0,
    week_number: initialData?.week_number || 1,
    topic: initialData?.topic || '',
    content: initialData?.content || '',
    learning_objectives: initialData?.learning_objectives || '',
  });

  useEffect(() => {
    const fetchCourses = async () => {
      try {
        const response = await fetch('/api/courses');
        const data = await response.json();
        setCourses(data);
      } catch (error) {
        console.error('Error fetching courses:', error);
      }
    };

    fetchCourses();
  }, []);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleCourseChange = (value: string) => {
    setFormData((prev) => ({ ...prev, course_id: Number(value) }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await onSubmit({
      ...formData,
      course_id: Number(formData.course_id),
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="course_id">Course</Label>
          <Select
            value={formData.course_id.toString()}
            onValueChange={handleCourseChange}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select a course" />
            </SelectTrigger>
            <SelectContent>
              {courses.map((course) => (
                <SelectItem key={course.course_id} value={course.course_id.toString()}>
                  {course.title}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="week_number">Week Number</Label>
          <Input
            id="week_number"
            name="week_number"
            type="number"
            min="1"
            value={formData.week_number}
            onChange={handleChange}
            required
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="topic">Topic</Label>
        <Input
          id="topic"
          name="topic"
          value={formData.topic}
          onChange={handleChange}
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="content">Content</Label>
        <Textarea
          id="content"
          name="content"
          value={formData.content}
          onChange={handleChange}
          required
          rows={5}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="learning_objectives">Learning Objectives</Label>
        <Textarea
          id="learning_objectives"
          name="learning_objectives"
          value={formData.learning_objectives}
          onChange={handleChange}
          rows={3}
        />
      </div>

      <Button type="submit" disabled={isLoading}>
        {isLoading ? 'Saving...' : 'Save Curriculum'}
      </Button>
    </form>
  );
};

export default CurriculumForm; 