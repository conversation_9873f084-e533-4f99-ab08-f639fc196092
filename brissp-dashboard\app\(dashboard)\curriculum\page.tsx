"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Plus, Pencil, Trash2 } from "lucide-react"
import Link from "next/link"
import { Curriculum } from "@/lib/types"

const CurriculumPage = () => {
  const [curriculumItems, setCurriculumItems] = useState<Curriculum[]>([])

  useEffect(() => {
    fetchCurriculum()
  }, [])

  const fetchCurriculum = async () => {
    try {
      const response = await fetch('/api/curriculum')
      const data = await response.json()
      setCurriculumItems(data)
    } catch (error) {
      console.error('Error fetching curriculum:', error)
    }
  }

  const handleDelete = async (curriculumId: number) => {
    if (confirm('Are you sure you want to delete this curriculum item?')) {
      try {
        const response = await fetch(`/api/curriculum/${curriculumId}`, {
          method: 'DELETE',
        })
        if (response.ok) {
          fetchCurriculum()
        }
      } catch (error) {
        console.error('Error deleting curriculum:', error)
      }
    }
  }

  return (
    <div className="p-6 ">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Curriculum Management</h1>
        <Link href="/curriculum/new">
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            Add New Curriculum
          </Button>
        </Link>
      </div>

      <div className="bg-white rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Course ID</TableHead>
              <TableHead>Week</TableHead>
              <TableHead>Topic</TableHead>
              <TableHead>Learning Objectives</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {curriculumItems.map((item) => (
              <TableRow key={item.curriculum_id}>
                <TableCell>{item.course_id}</TableCell>
                <TableCell>{item.week_number}</TableCell>
                <TableCell>{item.topic}</TableCell>
                <TableCell>{item.learning_objectives}</TableCell>
                <TableCell className="flex space-x-2">
                  <Link href={`/curriculum/${item.curriculum_id}/edit`}>
                    <Button variant="outline" size="sm">
                      <Pencil className="w-4 h-4" />
                    </Button>
                  </Link>
                  <Button 
                    variant="destructive" 
                    size="sm"
                    onClick={() => handleDelete(item.curriculum_id!)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}

export default CurriculumPage 