"use client"

import { useState, useEffect } from "react"
import { useRouter, usePara<PERSON> } from "next/navigation"
import RefresherCourseForm from "@/components/RefresherCourseForm"
import { Course } from "@/lib/types"

export default function EditRefresherCoursePage() {
  const router = useRouter()
  const params = useParams()
  const [course, setCourse] = useState<Course | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingData, setIsLoadingData] = useState(true)

  useEffect(() => {
    if (params.id) {
      fetchCourse()
    }
  }, [params.id])

  const fetchCourse = async () => {
    try {
      const response = await fetch(`/api/courses/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        setCourse(data)
      }
    } catch (error) {
      console.error('Error fetching course:', error)
    } finally {
      setIsLoadingData(false)
    }
  }

  const handleSubmit = async (data: Partial<Course>, imageFile?: File) => {
    setIsLoading(true)
    try {
      let imageUrl = data.image_url

      // Handle image upload if a new file is provided
      if (imageFile) {
        const formData = new FormData()
        formData.append('file', imageFile)

        const uploadResponse = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        })

        if (uploadResponse.ok) {
          const { url } = await uploadResponse.json()
          imageUrl = url
        }
      }

      // Update refresher course
      const response = await fetch(`/api/courses/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          image_url: imageUrl,
        }),
      })
      
      if (response.ok) {
        router.push('/courses')
      }
    } catch (error) {
      console.error('Error updating refresher course:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoadingData) {
    return (
      <div className="p-6">
        <div className="text-center">Loading course data...</div>
      </div>
    )
  }

  if (!course) {
    return (
      <div className="p-6">
        <div className="text-center text-red-600">Course not found</div>
      </div>
    )
  }

  if (course.program_type !== 'refresher') {
    return (
      <div className="p-6">
        <div className="text-center text-red-600">
          This is not a refresher course. Please use the regular course edit page.
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 ">
      <h1 className="text-2xl font-bold mb-6">Edit Refresher Course</h1>
      <div className="bg-white rounded-lg border p-6">
        <RefresherCourseForm 
          initialData={course} 
          onSubmit={handleSubmit} 
          isLoading={isLoading} 
        />
      </div>
    </div>
  )
}
