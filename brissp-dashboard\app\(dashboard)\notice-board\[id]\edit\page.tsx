"use client"
import React, { useEffect, useState } from 'react';
import NoticeForm from '@/components/NoticeForm';
import { Notice } from '@/lib/types';
import { useParams } from 'next/navigation';

const NoticeBoardEdit = () => {
  const params = useParams();
  const id = params?.id; // Use optional chaining to avoid null error
  const [notice, setNotice] = useState<Notice | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      const fetchNotice = async () => {
        try {
          const response = await fetch(`/api/notices/${id}`);
          
          if (!response.ok) {
            throw new Error(`Error: ${response.status}`);
          }
          
          const data = await response.json();
          
          // Ensure dates are properly formatted
          if (data.publish_date) {
            // Make sure it's in YYYY-MM-DD format for the form
            data.publish_date = data.publish_date.split('T')[0];
          }
          
          if (data.expiry_date) {
            // Make sure it's in YYYY-MM-DD format for the form
            data.expiry_date = data.expiry_date.split('T')[0];
          }
          
          setNotice(data);
        } catch (err) {
          console.error('Failed to fetch notice:', err);
          setError('Failed to load notice data. Please try again.');
        } finally {
          setIsLoading(false);
        }
      };
      
      fetchNotice();
    }
  }, [id]);

  const handleSubmit = async (data: Partial<Notice>) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/notices/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }
      
      // Use window.location for navigation
      window.location.href = '/notice-board';
    } catch (err) {
      console.error('Failed to update notice:', err);
      setError('Failed to update notice. Please try again.');
      setIsLoading(false);
    }
  };

  if (isLoading) return <div className="p-6">Loading...</div>;
  
  if (error) return <div className="p-6 text-red-500">{error}</div>;

  return (
    <div className="p-6 ">
      <h1 className="text-2xl font-bold mb-6">Edit Notice</h1>
      <div className="bg-white rounded-lg border p-6">
        {notice && (
          <NoticeForm
            initialData={notice}
            onSubmit={handleSubmit}
            isLoading={isLoading}
          />
        )}
        {!notice && !isLoading && (
          <div className="text-red-500">Notice not found</div>
        )}
      </div>
    </div>
  );
};

export default NoticeBoardEdit;