{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/components/Sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\"\r\nimport { useState, useEffect } from \"react\"\r\nimport { BarChart2, BellDot, GraduationCap, LogOut, ClipboardList, Award, Download, BookOpen, Layers, Key, Presentation, Briefcase, FileText, Menu, X } from \"lucide-react\"\r\nimport { useRouter } from \"next/navigation\"\r\nimport { toast } from \"sonner\"\r\n\r\ninterface SidebarProps {\r\n  isOpen?: boolean;\r\n  onToggle?: () => void;\r\n}\r\n\r\nconst Sidebar = ({ isOpen = false, onToggle }: SidebarProps) => {\r\n  const router = useRouter();\r\n  const [isMobile, setIsMobile] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const checkScreenSize = () => {\r\n      setIsMobile(window.innerWidth < 768);\r\n    };\r\n\r\n    checkScreenSize();\r\n    window.addEventListener('resize', checkScreenSize);\r\n\r\n    return () => window.removeEventListener('resize', checkScreenSize);\r\n  }, []);\r\n\r\n  const handleLogout = async () => {\r\n    try {\r\n      const response = await fetch(\"/api/admin/auth/logout\", {\r\n        method: \"POST\",\r\n        credentials: \"include\",\r\n      });\r\n\r\n      if (response.ok) {\r\n        toast.success(\"Logged out successfully\");\r\n        router.push(\"/\");\r\n      } else {\r\n        toast.error(\"Failed to log out\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Logout error:\", error);\r\n      toast.error(\"An error occurred during logout\");\r\n    }\r\n  };\r\n\r\n  const handleLinkClick = () => {\r\n    if (isMobile && onToggle) {\r\n      onToggle();\r\n    }\r\n  };\r\n\r\n  const navigationItems = [\r\n    { href: \"/panel\", icon: BarChart2, label: \"Dashboard\" },\r\n    { href: \"/applications\", icon: ClipboardList, label: \"Course Applications\" },\r\n    { href: \"/pitch-deck-applications\", icon: Presentation, label: \"Pitch Deck Applications\" },\r\n    { href: \"/internship-applications\", icon: Briefcase, label: \"Internship Applications\" },\r\n    { href: \"/fyp-applications\", icon: FileText, label: \"FYP Applications\" },\r\n    { href: \"/debug-internships\", icon: FileText, label: \"Debug Internships\" },\r\n    { href: \"/results\", icon: Award, label: \"Results\" },\r\n    { href: \"/downloadables\", icon: Download, label: \"Resources\" },\r\n    { href: \"/courses\", icon: BookOpen, label: \"Courses\" },\r\n    { href: \"/curriculum\", icon: Layers, label: \"Curriculum\" },\r\n    { href: \"/notice-board\", icon: BellDot, label: \"Notice Board\" },\r\n    { href: \"/graduates\", icon: GraduationCap, label: \"Graduates\" },\r\n    { href: \"/password-management\", icon: Key, label: \"Password Management\" },\r\n  ];\r\n\r\n  return (\r\n    <>\r\n      {/* Mobile Overlay */}\r\n      {isMobile && isOpen && (\r\n        <div\r\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\"\r\n          onClick={onToggle}\r\n        />\r\n      )}\r\n\r\n      {/* Sidebar */}\r\n      <div className={`\r\n        ${isMobile\r\n          ? `fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out ${\r\n              isOpen ? 'translate-x-0' : '-translate-x-full'\r\n            }`\r\n          : 'fixed inset-y-0 left-0 w-64'\r\n        }\r\n        border-r bg-white h-screen flex flex-col\r\n      `}>\r\n        {/* Header */}\r\n        <div className=\"p-4 border-b flex items-center justify-between\">\r\n          <Link href=\"/panel\" className=\"flex items-center space-x-2\" onClick={handleLinkClick}>\r\n            <span className=\"font-semibold text-lg\">Brissp Dash</span>\r\n          </Link>\r\n          {isMobile && (\r\n            <button\r\n              type=\"button\"\r\n              onClick={onToggle}\r\n              className=\"p-2 rounded-lg hover:bg-gray-100 md:hidden\"\r\n            >\r\n              <X className=\"w-5 h-5\" />\r\n            </button>\r\n          )}\r\n        </div>\r\n\r\n        {/* Navigation */}\r\n        <nav className=\"p-4 space-y-2 flex-grow overflow-y-auto\">\r\n          {navigationItems.map((item) => {\r\n            const Icon = item.icon;\r\n            return (\r\n              <Link\r\n                key={item.href}\r\n                href={item.href}\r\n                onClick={handleLinkClick}\r\n                className=\"flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-200\"\r\n              >\r\n                <Icon className=\"w-5 h-5 flex-shrink-0\" />\r\n                <span className=\"truncate\">{item.label}</span>\r\n              </Link>\r\n            );\r\n          })}\r\n        </nav>\r\n\r\n        {/* Logout Button */}\r\n        <div className=\"p-4 border-t mt-auto\">\r\n          <button\r\n            type=\"button\"\r\n            onClick={handleLogout}\r\n            className=\"flex items-center space-x-2 px-3 py-2 rounded-lg text-red-600 hover:bg-red-50 w-full transition-colors duration-200\"\r\n          >\r\n            <LogOut className=\"w-5 h-5 flex-shrink-0\" />\r\n            <span>Logout</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </>\r\n  )\r\n}\r\n\r\n// Mobile Header Component with Hamburger Menu\r\nexport const MobileHeader = ({ onToggle }: { onToggle: () => void }) => {\r\n  return (\r\n    <div className=\"md:hidden bg-white border-b px-4 py-3 flex items-center justify-between sticky top-0 z-30\">\r\n      <button\r\n        type=\"button\"\r\n        onClick={onToggle}\r\n        className=\"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200\"\r\n        aria-label=\"Toggle navigation menu\"\r\n        title=\"Toggle navigation menu\"\r\n      >\r\n        <Menu className=\"w-6 h-6\" />\r\n      </button>\r\n      <h1 className=\"font-semibold text-lg\">Brissp Dashboard</h1>\r\n      <div className=\"w-10\" /> {/* Spacer for centering */}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Sidebar"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AACA;AAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;;;AAaA,MAAM,UAAU,CAAC,EAAE,SAAS,KAAK,EAAE,QAAQ,EAAgB;;IACzD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM;qDAAkB;oBACtB,YAAY,OAAO,UAAU,GAAG;gBAClC;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAElC;qCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;4BAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,0BAA0B;gBACrD,QAAQ;gBACR,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,YAAY,UAAU;YACxB;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB;YAAE,MAAM;YAAU,MAAM,mOAAA,CAAA,YAAS;YAAE,OAAO;QAAY;QACtD;YAAE,MAAM;YAAiB,MAAM,2NAAA,CAAA,gBAAa;YAAE,OAAO;QAAsB;QAC3E;YAAE,MAAM;YAA4B,MAAM,qNAAA,CAAA,eAAY;YAAE,OAAO;QAA0B;QACzF;YAAE,MAAM;YAA4B,MAAM,+MAAA,CAAA,YAAS;YAAE,OAAO;QAA0B;QACtF;YAAE,MAAM;YAAqB,MAAM,iNAAA,CAAA,WAAQ;YAAE,OAAO;QAAmB;QACvE;YAAE,MAAM;YAAsB,MAAM,iNAAA,CAAA,WAAQ;YAAE,OAAO;QAAoB;QACzE;YAAE,MAAM;YAAY,MAAM,uMAAA,CAAA,QAAK;YAAE,OAAO;QAAU;QAClD;YAAE,MAAM;YAAkB,MAAM,6MAAA,CAAA,WAAQ;YAAE,OAAO;QAAY;QAC7D;YAAE,MAAM;YAAY,MAAM,iNAAA,CAAA,WAAQ;YAAE,OAAO;QAAU;QACrD;YAAE,MAAM;YAAe,MAAM,yMAAA,CAAA,SAAM;YAAE,OAAO;QAAa;QACzD;YAAE,MAAM;YAAiB,MAAM,+MAAA,CAAA,UAAO;YAAE,OAAO;QAAe;QAC9D;YAAE,MAAM;YAAc,MAAM,2NAAA,CAAA,gBAAa;YAAE,OAAO;QAAY;QAC9D;YAAE,MAAM;YAAwB,MAAM,mMAAA,CAAA,MAAG;YAAE,OAAO;QAAsB;KACzE;IAED,qBACE;;YAEG,YAAY,wBACX,6LAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAKb,6LAAC;gBAAI,WAAW,CAAC;QACf,EAAE,WACE,CAAC,yFAAyF,EACxF,SAAS,kBAAkB,qBAC3B,GACF,8BACH;;MAEH,CAAC;;kCAEC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAS,WAAU;gCAA8B,SAAS;0CACnE,cAAA,6LAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;4BAEzC,0BACC,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAMnB,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC;4BACpB,MAAM,OAAO,KAAK,IAAI;4BACtB,qBACE,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,SAAS;gCACT,WAAU;;kDAEV,6LAAC;wCAAK,WAAU;;;;;;kDAChB,6LAAC;wCAAK,WAAU;kDAAY,KAAK,KAAK;;;;;;;+BANjC,KAAK,IAAI;;;;;wBASpB;;;;;;kCAIF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAK;4BACL,SAAS;4BACT,WAAU;;8CAEV,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;AAMlB;GA5HM;;QACW,qIAAA,CAAA,YAAS;;;KADpB;AA+HC,MAAM,eAAe,CAAC,EAAE,QAAQ,EAA4B;IACjE,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,MAAK;gBACL,SAAS;gBACT,WAAU;gBACV,cAAW;gBACX,OAAM;0BAEN,cAAA,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;;;;;;0BAElB,6LAAC;gBAAG,WAAU;0BAAwB;;;;;;0BACtC,6LAAC;gBAAI,WAAU;;;;;;YAAS;;;;;;;AAG9B;MAhBa;uCAkBE"}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 346, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/components/AuthGuard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { toast } from \"sonner\";\r\n\r\ninterface AuthGuardProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nconst AuthGuard = ({ children }: AuthGuardProps) => {\r\n  const router = useRouter();\r\n  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);\r\n\r\n  useEffect(() => {\r\n    // Check if user is authenticated\r\n    const checkAuth = async () => {\r\n      try {\r\n        // Add credentials: 'include' to ensure cookies are sent with the request\r\n        const response = await fetch(\"/api/admin/auth/check\", {\r\n          method: \"GET\",\r\n          credentials: \"include\",\r\n          cache: \"no-store\"\r\n        });\r\n\r\n        if (!response.ok) {\r\n          throw new Error(\"Not authenticated\");\r\n        }\r\n\r\n        const data = await response.json();\r\n        console.log(\"Auth check response:\", data);\r\n        setIsAuthenticated(true);\r\n      } catch (error) {\r\n        console.error(\"Authentication check failed:\", error);\r\n        setIsAuthenticated(false);\r\n        toast.error(\"Please log in to access the dashboard\");\r\n        router.push(\"/\");\r\n      }\r\n    };\r\n\r\n    checkAuth();\r\n  }, [router]);\r\n\r\n  // Show loading state while checking authentication\r\n  if (isAuthenticated === null) {\r\n    return (\r\n      <div className=\"min-h-screen flex items-center justify-center\">\r\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // If authenticated, render children\r\n  return isAuthenticated ? <>{children}</> : null;\r\n};\r\n\r\nexport default AuthGuard;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAUA,MAAM,YAAY,CAAC,EAAE,QAAQ,EAAkB;;IAC7C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAEvE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,iCAAiC;YACjC,MAAM;iDAAY;oBAChB,IAAI;wBACF,yEAAyE;wBACzE,MAAM,WAAW,MAAM,MAAM,yBAAyB;4BACpD,QAAQ;4BACR,aAAa;4BACb,OAAO;wBACT;wBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;4BAChB,MAAM,IAAI,MAAM;wBAClB;wBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;wBAChC,QAAQ,GAAG,CAAC,wBAAwB;wBACpC,mBAAmB;oBACrB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,gCAAgC;wBAC9C,mBAAmB;wBACnB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wBACZ,OAAO,IAAI,CAAC;oBACd;gBACF;;YAEA;QACF;8BAAG;QAAC;KAAO;IAEX,mDAAmD;IACnD,IAAI,oBAAoB,MAAM;QAC5B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,oCAAoC;IACpC,OAAO,gCAAkB;kBAAG;wBAAe;AAC7C;GA5CM;;QACW,qIAAA,CAAA,YAAS;;;KADpB;uCA8CS"}}, {"offset": {"line": 428, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}