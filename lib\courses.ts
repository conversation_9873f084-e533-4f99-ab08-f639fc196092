export const courses = {
    'artificial-intelligence': {
      title: "Artificial Intelligence",
      subtitle: "Unlock The Power Of Artificial Intelligence And Become An AI Expert",
      duration: "3 Months Program",
      price: "K850/Pm",
      image: "/ai-course.jpg",
      description: "Our comprehensive AI program covers machine learning, deep learning, neural networks, and practical applications of AI in today's industry.",
      highlights: [
        "Hands-on experience with real-world AI projects",
        "Industry-standard tools and frameworks",
        "Personalized mentoring from AI experts",
        "Career guidance and placement support"
      ],
      curriculum: [
        {
          week: "Week 1-2",
          topics: ["Introduction to AI", "Python for AI", "Data Preprocessing"]
        },
        {
          week: "Week 3-4",
          topics: ["Machine Learning Fundamentals", "Supervised Learning", "Model Evaluation"]
        },
        {
          week: "Week 5-6",
          topics: ["Deep Learning", "Neural Networks", "Computer Vision"]
        },
        {
          week: "Week 7-8",
          topics: ["Natural Language Processing", "Reinforcement Learning", "AI Ethics"]
        },
        {
          week: "Week 9-12",
          topics: ["Capstone Project", "Industry Applications", "Portfolio Development"]
        }
      ],
      stats: [
        { label: "Hours per Week", value: "20", icon: "Clock" },
        { label: "Class Size", value: "15 Students", icon: "Users" },
        { label: "Projects", value: "8+", icon: "BookOpen" },
        { label: "Start Date", value: "Flexible", icon: "Calendar" }
      ]
    },
    'application-development': {
      title: "Application Development",
      subtitle: "Unlock The Power Of Artificial Intelligence And Become An AI Expert",
      duration: "3 Months Program",
      price: "K850/Pm",
      image: "/ai-course.jpg",
      description: "Our comprehensive AI program covers machine learning, deep learning, neural networks, and practical applications of AI in today's industry.",
      highlights: [
        "Hands-on experience with real-world AI projects",
        "Industry-standard tools and frameworks",
        "Personalized mentoring from AI experts",
        "Career guidance and placement support"
      ],
      curriculum: [
        {
          week: "Week 1-2",
          topics: ["Introduction to AI", "Python for AI", "Data Preprocessing"]
        },
        {
          week: "Week 3-4",
          topics: ["Machine Learning Fundamentals", "Supervised Learning", "Model Evaluation"]
        },
        {
          week: "Week 5-6",
          topics: ["Deep Learning", "Neural Networks", "Computer Vision"]
        },
        {
          week: "Week 7-8",
          topics: ["Natural Language Processing", "Reinforcement Learning", "AI Ethics"]
        },
        {
          week: "Week 9-12",
          topics: ["Capstone Project", "Industry Applications", "Portfolio Development"]
        }
      ],
      stats: [
        { label: "Hours per Week", value: "20", icon: "Clock" },
        { label: "Class Size", value: "15 Students", icon: "Users" },
        { label: "Projects", value: "8+", icon: "BookOpen" },
        { label: "Start Date", value: "Flexible", icon: "Calendar" }
      ]
    },
    'automation-and-robotics': {
      title: "Automation and Robotics",
      subtitle: "Unlock The Power Of Artificial Intelligence And Become An AI Expert",
      duration: "3 Months Program",
      price: "K850/Pm",
      image: "/ai-course.jpg",
      description: "Our comprehensive AI program covers machine learning, deep learning, neural networks, and practical applications of AI in today's industry.",
      highlights: [
        "Hands-on experience with real-world AI projects",
        "Industry-standard tools and frameworks",
        "Personalized mentoring from AI experts",
        "Career guidance and placement support"
      ],
      curriculum: [
        {
          week: "Week 1-2",
          topics: ["Introduction to AI", "Python for AI", "Data Preprocessing"]
        },
        {
          week: "Week 3-4",
          topics: ["Machine Learning Fundamentals", "Supervised Learning", "Model Evaluation"]
        },
        {
          week: "Week 5-6",
          topics: ["Deep Learning", "Neural Networks", "Computer Vision"]
        },
        {
          week: "Week 7-8",
          topics: ["Natural Language Processing", "Reinforcement Learning", "AI Ethics"]
        },
        {
          week: "Week 9-12",
          topics: ["Capstone Project", "Industry Applications", "Portfolio Development"]
        }
      ],
      stats: [
        { label: "Hours per Week", value: "20", icon: "Clock" },
        { label: "Class Size", value: "15 Students", icon: "Users" },
        { label: "Projects", value: "8+", icon: "BookOpen" },
        { label: "Start Date", value: "Flexible", icon: "Calendar" }
      ]
    },
    'graphic-designing': {
      title: "Graphic Designing",
      subtitle: "Master Modern Web Development and Build Real-World Applications",
      duration: "4 Months Program",
      price: "K750/Pm",
      image: "/web-dev.jpg",
      description: "Learn full-stack web development using the latest technologies and frameworks. Build responsive, scalable web applications from scratch.",
      highlights: [
        "Full-stack development coverage",
        "Modern frameworks and tools",
        "Real-world project experience",
        "Industry best practices"
      ],
      curriculum: [
        {
          week: "Week 1-2",
          topics: ["HTML5 & CSS3", "JavaScript Fundamentals", "Responsive Design"]
        },
        {
          week: "Week 3-4",
          topics: ["React.js", "State Management", "API Integration"]
        },
        {
          week: "Week 5-6",
          topics: ["Node.js", "Express.js", "Database Design"]
        },
        {
          week: "Week 7-8",
          topics: ["MongoDB", "Authentication", "Deployment"]
        },
        {
          week: "Week 9-16",
          topics: ["Full Stack Projects", "Testing", "Performance Optimization"]
        }
      ],
      stats: [
        { label: "Hours per Week", value: "25", icon: "Clock" },
        { label: "Class Size", value: "12 Students", icon: "Users" },
        { label: "Projects", value: "6+", icon: "BookOpen" },
        { label: "Start Date", value: "Monthly", icon: "Calendar" }
      ]
    }
  };

export const kidsCourses = {
  'robotics-automation': {
    title: "Robotics & Automation",
    subtitle: "Weekend Classes",
    startDate: "Monday, Sep 2nd, 2023",
    image: "/kids/robotics.jpg",
    description: "Robotics for kids: Building and programming robots. Hands-on activities foster problem-solving, creativity, tech understanding. Design, control robots, prepare for future innovation!",
    category: 'kids',
    tags: ['Robotics', 'STEM'],
    readMoreLink: '/courses/kids/robotics'
  },
  'space-science': {
    title: "Space Science",
    subtitle: "Weekend Classes",
    startDate: "Monday, Sep 2nd, 2023",
    image: "/kids/space-science.jpg",
    description: "Space science for kids: Explore the wonders of the universe! Engaging activities ignite curiosity, inspire discovery, and foster a passion for space exploration.",
    category: 'kids',
    tags: ['SpaceScience', 'STEM'],
    readMoreLink: '/courses/kids/space-science'
  },
  'coding': {
    title: "Coding",
    subtitle: "Weekend Classes",
    startDate: "Monday, Sep 2nd, 2023",
    image: "/kids/coding.jpg",
    description: "Coding for kids: Unlock the world of mobile app and web development! Learn essential coding skills, create interactive websites, design engaging mobile applications. Empower creativity and problem-solving abilities!",
    category: 'kids',
    tags: ['Coding', 'STEM'],
    readMoreLink: '/courses/kids/coding'
  }
};