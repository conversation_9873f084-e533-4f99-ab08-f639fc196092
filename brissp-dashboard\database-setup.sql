-- Database setup for Briisp Academy Dashboard
-- Run these commands to add the new tables for pitch deck, internship, and FYP applications

-- Create pitch deck applications table
CREATE TABLE IF NOT EXISTS pitch_deck_applications (
    application_id INT PRIMARY KEY AUTO_INCREMENT,
    applicant_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50) NOT NULL,
    company_name VA<PERSON><PERSON><PERSON>(255),
    industry VARCHAR(100),
    funding_stage ENUM('pre-seed', 'seed', 'series-a', 'series-b', 'later-stage') NOT NULL,
    funding_amount VARCHAR(100),
    business_description TEXT NOT NULL,
    target_audience TEXT,
    current_traction TEXT,
    team_size INT,
    previous_funding BOOLEAN DEFAULT false,
    pitch_deadline DATE,
    specific_requirements TEXT,
    preferred_start_date DATE,
    budget_range ENUM('basic', 'standard', 'premium', 'enterprise') NOT NULL,
    referral_source VARCHAR(100),
    status ENUM('pending', 'reviewed', 'accepted', 'rejected', 'in-progress', 'completed') DEFAULT 'pending',
    application_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reviewed_by INT,
    review_date TIMESTAMP NULL,
    review_notes TEXT,
    assigned_consultant VARCHAR(255),
    project_start_date DATE,
    project_completion_date DATE,
    final_pitch_deck_url VARCHAR(500),
    success_metrics TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_application_date (application_date),
    INDEX idx_email (email),
    INDEX idx_funding_stage (funding_stage)
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create final year project applications table
CREATE TABLE IF NOT EXISTS final_year_project_applications (
    application_id INT PRIMARY KEY AUTO_INCREMENT,
    student_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50) NOT NULL,
    university VARCHAR(255) NOT NULL,
    course_of_study VARCHAR(255) NOT NULL,
    year_of_study VARCHAR(20) NOT NULL,
    student_id VARCHAR(100),
    supervisor_name VARCHAR(255),
    supervisor_email VARCHAR(255),
    project_title VARCHAR(500),
    project_description TEXT,
    project_type ENUM('research', 'development', 'analysis', 'design', 'other') NOT NULL,
    research_area VARCHAR(255),
    methodology TEXT,
    expected_outcomes TEXT,
    timeline_weeks INT,
    required_resources TEXT,
    technical_requirements TEXT,
    preferred_supervisor_expertise VARCHAR(255),
    project_deadline DATE,
    defense_date DATE,
    university_requirements TEXT,
    additional_notes TEXT,
    status ENUM('pending', 'reviewed', 'accepted', 'rejected', 'in-progress', 'completed') DEFAULT 'pending',
    application_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reviewed_by INT,
    review_date TIMESTAMP NULL,
    review_notes TEXT,
    assigned_supervisor VARCHAR(255),
    project_start_date DATE,
    project_completion_date DATE,
    final_report_url VARCHAR(500),
    presentation_url VARCHAR(500),
    grade_achieved VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_application_date (application_date),
    INDEX idx_email (email),
    INDEX idx_university (university),
    INDEX idx_project_type (project_type)
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create internship applications table
CREATE TABLE IF NOT EXISTS internship_applications (
    application_id INT PRIMARY KEY AUTO_INCREMENT,
    applicant_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50) NOT NULL,
    date_of_birth DATE,
    gender ENUM('male', 'female', 'other', 'prefer-not-to-say'),
    nationality VARCHAR(100),
    address TEXT,
    city VARCHAR(100),
    province VARCHAR(100),
    postal_code VARCHAR(20),
    emergency_contact_name VARCHAR(255),
    emergency_contact_phone VARCHAR(50),
    emergency_contact_relationship VARCHAR(100),
    education_level ENUM('high-school', 'diploma', 'bachelors', 'masters', 'phd', 'other') NOT NULL,
    institution_name VARCHAR(255),
    field_of_study VARCHAR(255),
    graduation_year YEAR,
    current_gpa DECIMAL(3,2),
    relevant_coursework TEXT,
    programming_languages TEXT,
    technical_skills TEXT,
    soft_skills TEXT,
    previous_experience TEXT,
    portfolio_url VARCHAR(500),
    github_url VARCHAR(500),
    linkedin_url VARCHAR(500),
    cv_file_url VARCHAR(500),
    cover_letter TEXT,
    internship_type ENUM('web-development', 'mobile-development', 'data-science', 'ui-ux-design', 'digital-marketing', 'business-analysis', 'other') NOT NULL,
    preferred_duration ENUM('3-months', '6-months', '12-months', 'flexible') NOT NULL,
    preferred_start_date DATE,
    availability ENUM('full-time', 'part-time', 'flexible') NOT NULL,
    remote_preference ENUM('remote', 'on-site', 'hybrid', 'no-preference') NOT NULL,
    salary_expectation VARCHAR(100),
    transport_availability BOOLEAN DEFAULT false,
    willing_to_relocate BOOLEAN DEFAULT false,
    career_goals TEXT,
    why_briisp TEXT,
    additional_info TEXT,
    referral_source VARCHAR(100),
    status ENUM('pending', 'reviewed', 'interview-scheduled', 'interviewed', 'accepted', 'rejected', 'withdrawn') DEFAULT 'pending',
    application_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reviewed_by INT,
    review_date TIMESTAMP NULL,
    review_notes TEXT,
    interview_date DATETIME NULL,
    interview_notes TEXT,
    interviewer VARCHAR(255),
    internship_start_date DATE,
    internship_end_date DATE,
    supervisor_assigned VARCHAR(255),
    department_assigned VARCHAR(100),
    final_evaluation_score DECIMAL(3,2),
    completion_certificate_url VARCHAR(500),
    recommendation_letter_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_application_date (application_date),
    INDEX idx_email (email),
    INDEX idx_internship_type (internship_type),
    INDEX idx_education_level (education_level),
    INDEX idx_preferred_start_date (preferred_start_date)
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create pitch deck progress tracking table
CREATE TABLE IF NOT EXISTS pitch_deck_progress (
    progress_id INT PRIMARY KEY AUTO_INCREMENT,
    application_id INT NOT NULL,
    milestone ENUM('initial-consultation', 'market-research', 'deck-outline', 'content-creation', 'design-phase', 'review-feedback', 'final-delivery') NOT NULL,
    status ENUM('pending', 'in-progress', 'completed', 'on-hold') DEFAULT 'pending',
    start_date DATE,
    completion_date DATE,
    notes TEXT,
    deliverables_url VARCHAR(500),
    feedback TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (application_id) REFERENCES pitch_deck_applications(application_id) ON DELETE CASCADE,
    INDEX idx_application_milestone (application_id, milestone),
    INDEX idx_status (status)
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create final year project progress tracking table
CREATE TABLE IF NOT EXISTS fyp_progress (
    progress_id INT PRIMARY KEY AUTO_INCREMENT,
    application_id INT NOT NULL,
    milestone ENUM('proposal-review', 'literature-review', 'methodology-design', 'data-collection', 'analysis-phase', 'draft-writing', 'final-submission', 'defense-preparation') NOT NULL,
    status ENUM('pending', 'in-progress', 'completed', 'on-hold') DEFAULT 'pending',
    start_date DATE,
    completion_date DATE,
    notes TEXT,
    deliverables_url VARCHAR(500),
    supervisor_feedback TEXT,
    grade DECIMAL(3,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (application_id) REFERENCES final_year_project_applications(application_id) ON DELETE CASCADE,
    INDEX idx_application_milestone (application_id, milestone),
    INDEX idx_status (status)
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create internship progress tracking table
CREATE TABLE IF NOT EXISTS internship_progress (
    progress_id INT PRIMARY KEY AUTO_INCREMENT,
    application_id INT NOT NULL,
    week_number INT NOT NULL,
    learning_objectives TEXT,
    tasks_completed TEXT,
    skills_developed TEXT,
    challenges_faced TEXT,
    supervisor_feedback TEXT,
    self_assessment_score DECIMAL(3,2),
    supervisor_score DECIMAL(3,2),
    attendance_days INT,
    total_working_days INT,
    project_contributions TEXT,
    week_start_date DATE,
    week_end_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (application_id) REFERENCES internship_applications(application_id) ON DELETE CASCADE,
    INDEX idx_application_week (application_id, week_number),
    INDEX idx_week_dates (week_start_date, week_end_date)
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Insert sample data for testing (optional)
-- Uncomment the following lines if you want to add sample data

/*
-- Sample pitch deck application
INSERT INTO pitch_deck_applications (
    applicant_name, email, phone, company_name, industry, funding_stage,
    business_description, budget_range
) VALUES (
    'John Doe', '<EMAIL>', '+1234567890', 'TechStartup Inc', 'Technology',
    'seed', 'AI-powered customer service platform', 'standard'
);

-- Sample FYP application
INSERT INTO final_year_project_applications (
    student_name, email, phone, university, course_of_study, year_of_study,
    project_type, project_title, project_description
) VALUES (
    'Jane Smith', '<EMAIL>', '+1234567891', 'University of Technology',
    'Computer Science', 'Final Year', 'development',
    'Machine Learning for Predictive Analytics',
    'Developing a machine learning system for predictive analytics in business intelligence'
);

-- Sample internship application
INSERT INTO internship_applications (
    applicant_name, email, phone, education_level, internship_type,
    preferred_duration, availability, remote_preference
) VALUES (
    'Mike Johnson', '<EMAIL>', '+1234567892', 'bachelors',
    'web-development', '6-months', 'full-time', 'hybrid'
);
*/
