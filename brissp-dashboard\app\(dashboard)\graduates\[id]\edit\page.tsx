"use client"

import React, { useEffect, useState } from 'react';
import GraduateForm from '@/components/GraduateForm';
import { Graduate, Project } from '@/lib/types';
import { useParams } from 'next/navigation';

const GraduateEditPage = () => {
  const params = useParams();
  const id = params?.id; // Use optional chaining to avoid null error
  const [graduate, setGraduate] = useState<Graduate | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      const fetchGraduate = async () => {
        try {
          const response = await fetch(`/api/graduates/${id}`);
          if (!response.ok) {
            throw new Error(`Error: ${response.status}`);
          }
          const data = await response.json();
          
          // Process projects to convert date strings to Date objects
          if (data.projects && Array.isArray(data.projects)) {
            data.projects = data.projects.map((project: Project) => ({
              ...project,
              completion_date: project.completion_date ? new Date(project.completion_date) : null
            }));
          }
          
          setGraduate(data);
        } catch (err) {
          console.error('Failed to fetch graduate:', err);
          setError('Failed to load graduate data. Please try again.');
        } finally {
          setIsLoading(false);
        }
      };
  
      fetchGraduate();
    }
  }, [id]);

  const handleSubmit = async (data: Partial<Graduate>, certificateFile?: File | null) => {
    setIsLoading(true);
    setError(null);

    try {
      const formData = new FormData();
      if (certificateFile) {
        formData.append('certificate_file', certificateFile);
      }
      formData.append('graduateData', JSON.stringify(data));

      const response = await fetch(`/api/graduates/${id}`, {
        method: 'PUT',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      // Use window.location for navigation
      window.location.href = '/graduates';
    } catch (err) {
      console.error('Failed to update graduate:', err);
      setError('Failed to update graduate. Please try again.');
      setIsLoading(false);
    }
  };

  if (isLoading) return <div className="p-6">Loading...</div>;

  if (error) return <div className="p-6 text-red-500">{error}</div>;

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Edit Graduate</h1>
      <div className="bg-white rounded-lg border p-6">
        {graduate && (
          <GraduateForm
            initialData={graduate}
            onSubmit={handleSubmit}
            isLoading={isLoading}
          />
        )}
        {!graduate && !isLoading && (
          <div className="text-red-500">Graduate not found</div>
        )}
      </div>
    </div>
  );
};

export default GraduateEditPage;