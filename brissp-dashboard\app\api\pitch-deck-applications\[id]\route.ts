import { NextRequest, NextResponse } from 'next/server';
import pool from '@/lib/db';
import { RowDataPacket, ResultSetHeader } from 'mysql2';
import { PitchDeckApplication } from '../route';

// GET specific pitch deck application by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    if (!id) {
      return NextResponse.json(
        { error: 'Missing application ID' },
        { status: 400 }
      );
    }

    const [rows] = await pool.query<PitchDeckApplication[]>(
      'SELECT * FROM pitch_deck_applications WHERE application_id = ?',
      [id]
    );

    if (!rows[0]) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }

    // Get progress tracking data
    const [progressRows] = await pool.query<RowDataPacket[]>(
      `SELECT * FROM pitch_deck_progress 
       WHERE application_id = ? 
       ORDER BY milestone`,
      [id]
    );

    return NextResponse.json({
      application: rows[0],
      progress: progressRows
    });
  } catch (error) {
    console.error('Error fetching pitch deck application:', error);
    return NextResponse.json(
      { error: 'Failed to fetch application' },
      { status: 500 }
    );
  }
}

// PUT update specific pitch deck application
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    const data = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Missing application ID' },
        { status: 400 }
      );
    }

    const {
      status,
      review_notes,
      assigned_consultant,
      project_start_date,
      project_completion_date,
      final_pitch_deck_url,
      success_metrics
    } = data;

    const [result] = await pool.query<ResultSetHeader>(
      `UPDATE pitch_deck_applications 
       SET status = ?, review_notes = ?, assigned_consultant = ?, 
           project_start_date = ?, project_completion_date = ?, 
           final_pitch_deck_url = ?, success_metrics = ?, 
           review_date = NOW()
       WHERE application_id = ?`,
      [
        status, review_notes, assigned_consultant,
        project_start_date, project_completion_date,
        final_pitch_deck_url, success_metrics, id
      ]
    );

    if (result.affectedRows === 0) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Application updated successfully' });
  } catch (error) {
    console.error('Error updating pitch deck application:', error);
    return NextResponse.json(
      { error: 'Failed to update application' },
      { status: 500 }
    );
  }
}

// DELETE pitch deck application
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    if (!id) {
      return NextResponse.json(
        { error: 'Missing application ID' },
        { status: 400 }
      );
    }

    const [result] = await pool.query<ResultSetHeader>(
      'DELETE FROM pitch_deck_applications WHERE application_id = ?',
      [id]
    );

    if (result.affectedRows === 0) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Application deleted successfully' });
  } catch (error) {
    console.error('Error deleting pitch deck application:', error);
    return NextResponse.json(
      { error: 'Failed to delete application' },
      { status: 500 }
    );
  }
}
