import { NextRequest, NextResponse } from 'next/server';
import { authMiddleware, getCurrentAdmin } from '@/lib/auth';

export async function GET(req: NextRequest) {
  try {
    // Check if user is authenticated
    const authError = await authMiddleware(req);
    if (authError) return authError;
    
    // Get current admin user
    const admin = await getCurrentAdmin();
    if (!admin) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }
    
    return NextResponse.json({ user: admin });
  } catch (error) {
    console.error('Get current user error:', error);
    return NextResponse.json(
      { error: 'An error occurred while fetching user data' },
      { status: 500 }
    );
  }
}