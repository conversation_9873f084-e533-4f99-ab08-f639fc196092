/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { useState, useEffect } from 'react';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ResultForm } from './components/ResultForm';
import { ResultsTable } from './components/ResultsTable';
import { ResultsAnalytics } from './components/ResultsAnalytics';
import { StudentResultsAnalysis } from './components/StudentResultsAnalysis';
import { Course, Result } from './types';
import { toast } from 'sonner';

export default function ResultsPage() {
  const [courses, setCourses] = useState<Course[]>([]);
  const [selectedCourse, setSelectedCourse] = useState<string>('');
  const [results, setResults] = useState<Result[]>([]);
  const [analytics, setAnalytics] = useState<{
    averageScore: number;
    passRate: number;
    assessmentTypeBreakdown: Array<{ name: string; value: number }>;
  }>({
    averageScore: 0,
    passRate: 0,
    assessmentTypeBreakdown: [],
  });

  useEffect(() => {
    fetchCourses();
  }, []);

  // Independent useEffect for fetching results
  useEffect(() => {
    fetchResults();
  }, []); // This will run once when component mounts

  // This useEffect now only handles setting the default selected course
  useEffect(() => {
    if (courses.length > 0 && !selectedCourse) {
      setSelectedCourse(courses[0].course_id.toString());
    }
  }, [courses, selectedCourse]);

  const fetchCourses = async () => {
    try {
      const response = await fetch('/api/courses');
      const data = await response.json();
      setCourses(data);
    } catch {
      toast.error('Failed to fetch courses');
    }
  };

  const fetchResults = async () => {
    try {
      // If there's a selected course, use it as a filter, otherwise fetch all results
      const url = selectedCourse 
        ? `/api/results?courseId=${selectedCourse}`
        : '/api/results';
        
      const response = await fetch(url);
      const data = await response.json();
      setResults(data.results);
      calculateAnalytics(data.results);
    } catch {
      toast.error('Failed to fetch results');
    }
  };

  const calculateAnalytics = (results: Result[]) => {
      // Handle empty results case
      if (!results || results.length === 0) {
        setAnalytics({
          averageScore: 0,
          passRate: 0,
          assessmentTypeBreakdown: [],
        });
        return;
      }
      
      const avg = results.reduce((acc, curr) => 
        acc + (curr.score / curr.max_score) * 100, 0) / results.length;
      
      const passed = results.filter(r => r.is_passed).length;
      const passRate = (passed / results.length) * 100;
  
      const breakdown = Object.entries(
        results.reduce((acc: Record<string, number>, curr) => {
          acc[curr.assessment_type] = (acc[curr.assessment_type] || 0) + 1;
          return acc;
        }, {})
      ).map(([name, value]) => ({ name, value }));
  
      setAnalytics({
        averageScore: avg,
        passRate: passRate,
        assessmentTypeBreakdown: breakdown,
      });
    };

  const [editingResult, setEditingResult] = useState<Result | null>(null);

    const handleEdit = (result: Result) => {
      setEditingResult(result);
    };
  
    const handleUpdate = async (updatedResult: Result) => {
      try {
        const response = await fetch(`/api/results/${updatedResult.result_id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(updatedResult),
        });
  
        if (!response.ok) throw new Error('Failed to update result');
        
        toast.success('Result updated successfully');
        fetchResults(); // Refresh the results list
        setEditingResult(null); // Close the edit dialog
      } catch {
        toast.error('Failed to update result');
      }
    };
  
    return (
      <div className="p-6 container space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Results Management</h1>
        </div>
  
        <Tabs defaultValue="add" className="space-y-6">
          <TabsList>
            <TabsTrigger value="add">Add Result</TabsTrigger>
            <TabsTrigger value="view">View Results</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="student">Student Analysis</TabsTrigger>
          </TabsList>
  
          <TabsContent value="add">
            <ResultForm 
              courses={courses}
              selectedCourse={selectedCourse}
              onCourseSelect={setSelectedCourse}
            />
          </TabsContent>
  
          <TabsContent value="view">
            <ResultsTable 
              results={results}
              onEdit={handleEdit}
              editingResult={editingResult}
              onUpdate={handleUpdate}
              onCancelEdit={() => setEditingResult(null)}
            />
          </TabsContent>
  
          <TabsContent value="analytics">
            <ResultsAnalytics analytics={analytics} />
          </TabsContent>
  
          <TabsContent value="student">
            <StudentResultsAnalysis courses={courses} />
          </TabsContent>
        </Tabs>
      </div>
    );
  }
  