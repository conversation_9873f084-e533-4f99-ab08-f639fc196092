"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Plus, Pencil, Trash2 } from "lucide-react"
import Link from "next/link"
import { Graduate } from "@/lib/types"

const GraduatesPage = () => {
  const [graduates, setGraduates] = useState<Graduate[]>([])

  useEffect(() => {
    fetchGraduates()
  }, [])

  const fetchGraduates = async () => {
    try {
      const response = await fetch('/api/graduates')
      const data = await response.json()
      setGraduates(data)
    } catch (error) {
      console.error('Error fetching graduates:', error)
    }
  }

  const handleDelete = async (graduateId: number) => {
    if (confirm('Are you sure you want to delete this graduate?')) {
      try {
        const response = await fetch(`/api/graduates/${graduateId}`, {
          method: 'DELETE',
        })
        if (response.ok) {
          fetchGraduates()
        }
      } catch (error) {
        console.error('Error deleting graduate:', error)
      }
    }
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Graduates Management</h1>
        <Link href="/graduates/new">
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            Add New Graduate
          </Button>
        </Link>
      </div>

      <div className="bg-white rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Year of Completion</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {graduates.map((graduate) => (
              <TableRow key={graduate.graduate_id}>
                <TableCell>{graduate.name}</TableCell>
                <TableCell>{graduate.email}</TableCell>
                <TableCell>{graduate.year_of_completion}</TableCell>
                <TableCell className="flex space-x-2">
                  <Link href={`/graduates/${graduate.graduate_id}/edit`}>
                    <Button variant="outline" size="sm">
                      <Pencil className="w-4 h-4" />
                    </Button>
                  </Link>
                  <Button 
                    variant="destructive" 
                    size="sm"
                    onClick={() => handleDelete(graduate.graduate_id!)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}

export default GraduatesPage