-- Create the courses table (unchanged)
CREATE TABLE courses (
    course_id INT PRIMARY KEY AUTO_INCREMENT,
    course_code VARCHAR(20) NOT NULL UNIQUE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    duration_months INT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    department VARCHAR(100) NOT NULL,
    category ENUM('adults', 'kids') NOT NULL,
    image_url VARCHAR(255),
    program_type VARCHAR(100) NOT NULL,
    num_lectures INT NOT NULL,
    skill_level ENUM('beginner', 'intermediate', 'advanced') NOT NULL,
    languages VARCHAR(255) NOT NULL,
    class_days VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create the curriculum table (unchanged)
CREATE TABLE curriculum (
    curriculum_id INT PRIMARY KEY AUTO_INCREMENT,
    course_id INT NOT NULL,
    week_number INT NOT NULL,
    topic VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    learning_objectives TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE,
    UNIQUE KEY unique_week_per_course (course_id, week_number)
);

-- Create the gallery table (unchanged)
CREATE TABLE gallery (
    gallery_id INT PRIMARY KEY AUTO_INCREMENT,
    course_id INT NOT NULL,
    image_url VARCHAR(255) NOT NULL,
    image_title VARCHAR(100),
    image_description TEXT,
    image_type ENUM('cover', 'banner', 'content', 'thumbnail') NOT NULL,
    display_order INT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE,
    INDEX idx_course_images (course_id, is_active)
);

-- Create the notice_board table
CREATE TABLE notice_board (
    notice_id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    author VARCHAR(100),
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    publish_date DATE NOT NULL,
    expiry_date DATE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active_notices (is_active, publish_date, expiry_date)
);

-- Create the downloadable_files table
CREATE TABLE downloadable_files (
    file_id INT PRIMARY KEY AUTO_INCREMENT,
    course_id INT,
    title VARCHAR(255) NOT NULL,
    file_url VARCHAR(255) NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    file_size INT COMMENT 'Size in bytes',
    description TEXT,
    download_count INT DEFAULT 0,
    is_public BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE SET NULL,
    INDEX idx_course_files (course_id, is_active),
    INDEX idx_public_files (is_public, is_active)
);
