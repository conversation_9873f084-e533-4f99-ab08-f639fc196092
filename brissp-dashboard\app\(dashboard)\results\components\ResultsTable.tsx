'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON>alogHeader, <PERSON>alogT<PERSON><PERSON>, Di<PERSON>Trigger, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Result } from '../types';

interface ResultsTableProps {
  results: Result[];
  onEdit: (result: Result) => void;
  editingResult: Result | null;
  onUpdate: (result: Result) => Promise<void>;
  onCancelEdit: () => void;
}

export function ResultsTable({ 
  results, 
  onEdit, 
  editingResult, 
  onUpdate, 
  onCancelEdit 
}: ResultsTableProps) {
  const [editForm, setEditForm] = useState<Result | null>(null);

  // Initialize edit form when editingResult changes
  if (editingResult && (!editForm || editForm.result_id !== editingResult.result_id)) {
    setEditForm({...editingResult});
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (editForm) {
      onUpdate(editForm);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Results List</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Student</TableHead>
                <TableHead>Assessment</TableHead>
                <TableHead>Score</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {results.map((result) => (
                <TableRow key={result.result_id}>
                  <TableCell>{`${result.first_name} ${result.last_name}`}</TableCell>
                  <TableCell>
                    <div className="font-medium">{result.assessment_title}</div>
                    <div className="text-sm text-gray-500">{result.assessment_type}</div>
                  </TableCell>
                  <TableCell>{`${result.score}/${result.max_score}`}</TableCell>
                  <TableCell>{new Date(result.result_date).toLocaleDateString()}</TableCell>
                  <TableCell>
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      result.is_passed ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {result.is_passed ? 'Passed' : 'Failed'}
                    </span>
                  </TableCell>
                  <TableCell>
                    <Dialog open={editingResult?.result_id === result.result_id} onOpenChange={(open) => {
                      if (!open) onCancelEdit();
                      if (open) onEdit(result);
                    }}>
                      <DialogTrigger asChild>
                        <Button variant="outline" size="sm">
                          Edit
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-[425px]">
                        <DialogHeader>
                          <DialogTitle>Edit Result</DialogTitle>
                        </DialogHeader>
                        {editForm && (
                          <form onSubmit={handleSubmit} className="space-y-4">
                            <div className="grid gap-2">
                              <Label htmlFor="edit-assessment-type">Assessment Type</Label>
                              <Select 
                                value={editForm.assessment_type} 
                                onValueChange={(value) => setEditForm({...editForm, assessment_type: value})}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Select assessment type" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="quiz">Quiz</SelectItem>
                                  <SelectItem value="exam">Exam</SelectItem>
                                  <SelectItem value="assignment">Assignment</SelectItem>
                                  <SelectItem value="project">Project</SelectItem>
                                  <SelectItem value="final">Final</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            <div className="grid gap-2">
                              <Label htmlFor="edit-assessment-title">Assessment Title</Label>
                              <Input 
                                id="edit-assessment-title"
                                value={editForm.assessment_title}
                                onChange={(e) => setEditForm({...editForm, assessment_title: e.target.value})}
                              />
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                              <div className="grid gap-2">
                                <Label htmlFor="edit-score">Score</Label>
                                <Input 
                                  id="edit-score"
                                  type="number"
                                  value={editForm.score}
                                  onChange={(e) => setEditForm({...editForm, score: parseFloat(e.target.value)})}
                                />
                              </div>
                              <div className="grid gap-2">
                                <Label htmlFor="edit-max-score">Max Score</Label>
                                <Input 
                                  id="edit-max-score"
                                  type="number"
                                  value={editForm.max_score}
                                  onChange={(e) => setEditForm({...editForm, max_score: parseFloat(e.target.value)})}
                                />
                              </div>
                            </div>

                            <div className="grid gap-2">
                              <Label htmlFor="edit-result-date">Result Date</Label>
                              <Input 
                                id="edit-result-date"
                                type="date"
                                value={editForm.result_date.split('T')[0]}
                                onChange={(e) => setEditForm({...editForm, result_date: e.target.value})}
                              />
                            </div>

                            <div className="grid gap-2">
                              <Label htmlFor="edit-comments">Comments</Label>
                              <Textarea 
                                id="edit-comments"
                                value={editForm.comments}
                                onChange={(e) => setEditForm({...editForm, comments: e.target.value})}
                                rows={3}
                              />
                            </div>

                            <div className="flex items-center space-x-2">
                              <Checkbox 
                                id="edit-is-passed"
                                checked={editForm.is_passed}
                                onCheckedChange={(checked) => 
                                  setEditForm({...editForm, is_passed: checked as boolean})
                                }
                              />
                              <Label htmlFor="edit-is-passed" className="font-normal">
                                Mark as passed
                              </Label>
                            </div>

                            <DialogFooter>
                              <Button type="button" variant="outline" onClick={onCancelEdit}>
                                Cancel
                              </Button>
                              <Button type="submit">Save Changes</Button>
                            </DialogFooter>
                          </form>
                        )}
                      </DialogContent>
                    </Dialog>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}