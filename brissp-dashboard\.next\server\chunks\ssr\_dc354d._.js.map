{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n\r\nexport function formatBytes(bytes: number, decimals = 2) {\r\n  if (bytes === 0) return '0 Bytes';\r\n  \r\n  const k = 1024;\r\n  const dm = decimals < 0 ? 0 : decimals;\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];\r\n  \r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n  \r\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\r\n}\r\n\r\n/**\r\n * Generates a random password with specified length\r\n * @param length Length of the password (default: 10)\r\n * @returns Random password string\r\n */\r\nexport function generateRandomPassword(length: number = 10): string {\r\n  const charset = \"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*\";\r\n  let password = \"\";\r\n  \r\n  for (let i = 0; i < length; i++) {\r\n    const randomIndex = Math.floor(Math.random() * charset.length);\r\n    password += charset[randomIndex];\r\n  }\r\n  \r\n  return password;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa,EAAE,WAAW,CAAC;IACrD,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,KAAK,WAAW,IAAI,IAAI;IAC9B,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;IAEvE,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,OAAO,MAAM,KAAK,CAAC,EAAE;AAC1E;AAOO,SAAS,uBAAuB,SAAiB,EAAE;IACxD,MAAM,UAAU;IAChB,IAAI,WAAW;IAEf,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,MAAM;QAC7D,YAAY,OAAO,CAAC,YAAY;IAClC;IAEA,OAAO;AACT"}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,sMAAM,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/components/ui/table.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Table = React.forwardRef<\r\n  HTMLTableElement,\r\n  React.HTMLAttributes<HTMLTableElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div className=\"relative w-full overflow-auto\">\r\n    <table\r\n      ref={ref}\r\n      className={cn(\"w-full caption-bottom text-sm\", className)}\r\n      {...props}\r\n    />\r\n  </div>\r\n))\r\nTable.displayName = \"Table\"\r\n\r\nconst TableHeader = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\r\n))\r\nTableHeader.displayName = \"TableHeader\"\r\n\r\nconst TableBody = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tbody\r\n    ref={ref}\r\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nTableBody.displayName = \"TableBody\"\r\n\r\nconst TableFooter = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tfoot\r\n    ref={ref}\r\n    className={cn(\r\n      \"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTableFooter.displayName = \"TableFooter\"\r\n\r\nconst TableRow = React.forwardRef<\r\n  HTMLTableRowElement,\r\n  React.HTMLAttributes<HTMLTableRowElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tr\r\n    ref={ref}\r\n    className={cn(\r\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTableRow.displayName = \"TableRow\"\r\n\r\nconst TableHead = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.ThHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <th\r\n    ref={ref}\r\n    className={cn(\r\n      \"h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTableHead.displayName = \"TableHead\"\r\n\r\nconst TableCell = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.TdHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <td\r\n    ref={ref}\r\n    className={cn(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nTableCell.displayName = \"TableCell\"\r\n\r\nconst TableCaption = React.forwardRef<\r\n  HTMLTableCaptionElement,\r\n  React.HTMLAttributes<HTMLTableCaptionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <caption\r\n    ref={ref}\r\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nTableCaption.displayName = \"TableCaption\"\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,sMAAM,UAAU,CAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIf,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAM,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAa,GAAG,KAAK;;;;;;AAEzE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,sMAAM,UAAU,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,sMAAM,UAAU,CAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,sMAAM,UAAU,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,oGACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,sMAAM,UAAU,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;QAC/D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,6BAAe,sMAAM,UAAU,CAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG"}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 222, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\r\n        outline: \"text-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE"}}, {"offset": {"line": 258, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 264, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,sMAAM,UAAU,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,sMAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,sMAAM,UAAU,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,sMAAM,UAAU,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,sMAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG"}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Select = SelectPrimitive.Root\r\n\r\nconst SelectGroup = SelectPrimitive.Group\r\n\r\nconst SelectValue = SelectPrimitive.Value\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n))\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n))\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n))\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n))\r\nSelectContent.displayName = SelectPrimitive.Content.displayName\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n))\r\nSelectItem.displayName = SelectPrimitive.Item.displayName\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAIA;AAHA;AACA;AAAA;AAAA;AAJA;;;;;;AAQA,MAAM,SAAS,mKAAgB,IAAI;AAEnC,MAAM,cAAc,mKAAgB,KAAK;AAEzC,MAAM,cAAc,mKAAgB,KAAK;AAEzC,MAAM,8BAAgB,sMAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,mKAAgB,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,mKAAgB,IAAI;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,mKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,qCAAuB,sMAAM,UAAU,CAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,cAAc;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,mKAAgB,cAAc,CAAC,WAAW;AAE7E,MAAM,uCAAyB,sMAAM,UAAU,CAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,gBAAgB;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,mKAAgB,gBAAgB,CAAC,WAAW;AAE9C,MAAM,8BAAgB,sMAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,mKAAgB,MAAM;kBACrB,cAAA,8OAAC,mKAAgB,OAAO;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,mKAAgB,QAAQ;oBACvB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,mKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,2BAAa,sMAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,mKAAgB,IAAI;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,mKAAgB,aAAa;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,mKAAgB,QAAQ;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,mKAAgB,IAAI,CAAC,WAAW;AAEzD,MAAM,gCAAkB,sMAAM,UAAU,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,SAAS;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,mKAAgB,SAAS,CAAC,WAAW"}}, {"offset": {"line": 531, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 537, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,sMAAM,UAAU,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG"}}, {"offset": {"line": 560, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 566, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/work/Brissp/briisp-academy/brissp-dashboard/app/%28dashboard%29/pitch-deck-applications/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Input } from \"@/components/ui/input\"\nimport { Eye,  Trash2, Search,  Presentation, TrendingUp, Clock, CheckCircle } from \"lucide-react\"\nimport Link from \"next/link\"\nimport { PitchDeckApplication } from \"@/app/api/pitch-deck-applications/route\"\n\nexport default function PitchDeckApplicationsPage() {\n  const [applications, setApplications] = useState<PitchDeckApplication[]>([])\n  const [loading, setLoading] = useState(true)\n  const [statusFilter, setStatusFilter] = useState<string>(\"all\")\n  const [searchTerm, setSearchTerm] = useState(\"\")\n  const [pagination, setPagination] = useState({\n    page: 1,\n    limit: 10,\n    total: 0,\n    totalPages: 0\n  })\n  const [stats, setStats] = useState({\n    total: 0,\n    pending: 0,\n    inProgress: 0,\n    completed: 0\n  })\n\n  useEffect(() => {\n    fetchApplications()\n    fetchStats()\n  }, [pagination.page, statusFilter])\n\n  const fetchApplications = async () => {\n    try {\n      setLoading(true)\n      const params = new URLSearchParams({\n        page: pagination.page.toString(),\n        limit: pagination.limit.toString(),\n      })\n\n      console.log('Fetching applications with statusFilter:', statusFilter)\n\n      if (statusFilter && statusFilter !== 'all') {\n        params.append('status', statusFilter)\n        console.log('Adding status filter to API call:', statusFilter)\n      } else {\n        console.log('Not adding status filter (showing all)')\n      }\n\n      const apiUrl = `/api/pitch-deck-applications?${params}`\n      console.log('API URL:', apiUrl)\n\n      const response = await fetch(apiUrl)\n      const data = await response.json()\n\n      console.log('Pitch Deck Applications API Response:', data)\n      console.log('Applications received:', data.applications?.length || 0)\n\n      if (data.applications && data.applications.length > 0) {\n        console.log('Sample application data:', data.applications[0])\n      }\n\n      setApplications(data.applications || [])\n      setPagination(prev => ({\n        ...prev,\n        total: data.pagination?.total || 0,\n        totalPages: data.pagination?.totalPages || 0\n      }))\n    } catch (error) {\n      console.error('Error fetching applications:', error)\n      setApplications([])\n      alert('Error fetching applications. Please check the console for details.')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const fetchStats = async () => {\n    try {\n      // Fetch stats for different statuses\n      const [totalRes, pendingRes, inProgressRes, completedRes] = await Promise.all([\n        fetch('/api/pitch-deck-applications'),\n        fetch('/api/pitch-deck-applications?status=pending'),\n        fetch('/api/pitch-deck-applications?status=in-progress'),\n        fetch('/api/pitch-deck-applications?status=completed')\n      ])\n\n      const [total, pending, inProgress, completed] = await Promise.all([\n        totalRes.json(),\n        pendingRes.json(),\n        inProgressRes.json(),\n        completedRes.json()\n      ])\n\n      setStats({\n        total: total.pagination?.total || 0,\n        pending: pending.pagination?.total || 0,\n        inProgress: inProgress.pagination?.total || 0,\n        completed: completed.pagination?.total || 0\n      })\n    } catch (error) {\n      console.error('Error fetching stats:', error)\n    }\n  }\n\n  const handleDelete = async (applicationId: number) => {\n    if (confirm('Are you sure you want to delete this application?')) {\n      try {\n        const response = await fetch(`/api/pitch-deck-applications/${applicationId}`, {\n          method: 'DELETE',\n        })\n        if (response.ok) {\n          fetchApplications()\n          fetchStats()\n        }\n      } catch (error) {\n        console.error('Error deleting application:', error)\n      }\n    }\n  }\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      pending: { variant: \"secondary\" as const, label: \"Pending\" },\n      reviewed: { variant: \"outline\" as const, label: \"Reviewed\" },\n      accepted: { variant: \"default\" as const, label: \"Accepted\" },\n      rejected: { variant: \"destructive\" as const, label: \"Rejected\" },\n      \"in-progress\": { variant: \"default\" as const, label: \"In Progress\" },\n      completed: { variant: \"default\" as const, label: \"Completed\" }\n    }\n    \n    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending\n    return <Badge variant={config.variant}>{config.label}</Badge>\n  }\n\n  const getFundingStageBadge = (stage: string) => {\n    const stageColors = {\n      \"pre-seed\": \"bg-blue-100 text-blue-800\",\n      \"seed\": \"bg-green-100 text-green-800\",\n      \"series-a\": \"bg-purple-100 text-purple-800\",\n      \"series-b\": \"bg-orange-100 text-orange-800\",\n      \"later-stage\": \"bg-red-100 text-red-800\"\n    }\n    \n    return (\n      <span className={`px-2 py-1 rounded-full text-xs font-medium ${stageColors[stage as keyof typeof stageColors] || 'bg-gray-100 text-gray-800'}`}>\n        {stage.replace('-', ' ').toUpperCase()}\n      </span>\n    )\n  }\n\n  const filteredApplications = applications.filter(app => {\n  \n\n    // Status filter check\n    const statusMatch = statusFilter === 'all' || app.status === statusFilter;\n\n    // Search term check\n    const searchMatch = !searchTerm ||\n      (app.applicant_name && app.applicant_name.toLowerCase().includes(searchTerm.toLowerCase())) ||\n      (app.email && app.email.toLowerCase().includes(searchTerm.toLowerCase())) ||\n      (app.company_name && app.company_name.toLowerCase().includes(searchTerm.toLowerCase()));\n\n    const result = statusMatch && searchMatch;\n\n    console.log('Filter result:', {\n      statusMatch,\n      searchMatch,\n      finalResult: result\n    });\n\n    return result;\n  })\n\n  return (\n    <div className=\"p-6 space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-3xl font-bold\">Pitch Deck Applications</h1>\n          <p className=\"text-gray-600\">Manage startup pitch deck development applications</p>\n        </div>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Total Applications</CardTitle>\n            <Presentation className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{stats.total}</div>\n          </CardContent>\n        </Card>\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Pending Review</CardTitle>\n            <Clock className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{stats.pending}</div>\n          </CardContent>\n        </Card>\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">In Progress</CardTitle>\n            <TrendingUp className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{stats.inProgress}</div>\n          </CardContent>\n        </Card>\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Completed</CardTitle>\n            <CheckCircle className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{stats.completed}</div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Filters */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Filters</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"flex gap-4\">\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />\n                <Input\n                  placeholder=\"Search by name, email, or company...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"pl-10\"\n                />\n              </div>\n            </div>\n            <Select value={statusFilter} onValueChange={setStatusFilter}>\n              <SelectTrigger className=\"w-48\">\n                <SelectValue placeholder=\"Filter by status\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"all\">All Statuses</SelectItem>\n                <SelectItem value=\"pending\">Pending</SelectItem>\n                <SelectItem value=\"reviewed\">Reviewed</SelectItem>\n                <SelectItem value=\"accepted\">Accepted</SelectItem>\n                <SelectItem value=\"rejected\">Rejected</SelectItem>\n                <SelectItem value=\"in-progress\">In Progress</SelectItem>\n                <SelectItem value=\"completed\">Completed</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n        </CardContent>\n      </Card>\n\n\n\n      {/* Applications Table */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Applications ({filteredApplications.length})</CardTitle>\n        </CardHeader>\n        <CardContent>\n          {loading ? (\n            <div className=\"text-center py-8\">Loading applications...</div>\n          ) : applications.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <p>No pitch deck applications found.</p>\n              <p className=\"text-sm text-gray-500 mt-2\">\n                Make sure the database table exists and contains data.\n              </p>\n            </div>\n          ) : filteredApplications.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <p>No applications match your current filters.</p>\n              <p className=\"text-sm text-gray-500 mt-2\">\n                Try adjusting your search terms or filters.\n              </p>\n            </div>\n          ) : (\n            <Table>\n              <TableHeader>\n                <TableRow>\n                  <TableHead>Applicant</TableHead>\n                  <TableHead>Company</TableHead>\n                  <TableHead>Funding Stage</TableHead>\n                  <TableHead>Budget Range</TableHead>\n                  <TableHead>Status</TableHead>\n                  <TableHead>Applied Date</TableHead>\n                  <TableHead>Actions</TableHead>\n                </TableRow>\n              </TableHeader>\n              <TableBody>\n                {filteredApplications.map((application) => (\n                  <TableRow key={application.application_id}>\n                    <TableCell>\n                      <div>\n                        <div className=\"font-medium\">{application.applicant_name}</div>\n                        <div className=\"text-sm text-gray-500\">{application.email}</div>\n                      </div>\n                    </TableCell>\n                    <TableCell>{application.company_name || 'N/A'}</TableCell>\n                    <TableCell>{getFundingStageBadge(application.funding_stage)}</TableCell>\n                    <TableCell>\n                      <span className=\"capitalize\">{application.budget_range}</span>\n                    </TableCell>\n                    <TableCell>{getStatusBadge(application.status)}</TableCell>\n                    <TableCell>{new Date(application.application_date).toLocaleDateString()}</TableCell>\n                    <TableCell>\n                      <div className=\"flex space-x-2\">\n                        <Link href={`/pitch-deck-applications/${application.application_id}`}>\n                          <Button variant=\"outline\" size=\"sm\">\n                            <Eye className=\"w-4 h-4\" />\n                          </Button>\n                        </Link>\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => handleDelete(application.application_id)}\n                        >\n                          <Trash2 className=\"w-4 h-4\" />\n                        </Button>\n                      </div>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Pagination */}\n      {pagination.totalPages > 1 && (\n        <div className=\"flex justify-center space-x-2\">\n          <Button\n            variant=\"outline\"\n            onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}\n            disabled={pagination.page === 1}\n          >\n            Previous\n          </Button>\n          <span className=\"flex items-center px-4\">\n            Page {pagination.page} of {pagination.totalPages}\n          </span>\n          <Button\n            variant=\"outline\"\n            onClick={() => setPagination(prev => ({ ...prev, page: Math.min(prev.totalPages, prev.page + 1) }))}\n            disabled={pagination.page === pagination.totalPages}\n          >\n            Next\n          </Button>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AADA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;;;;AAae,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,EAAE;IAC3E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,MAAM;QACN,OAAO;QACP,OAAO;QACP,YAAY;IACd;IACA,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACjC,OAAO;QACP,SAAS;QACT,YAAY;QACZ,WAAW;IACb;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;IACF,GAAG;QAAC,WAAW,IAAI;QAAE;KAAa;IAElC,MAAM,oBAAoB;QACxB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,IAAI,gBAAgB;gBACjC,MAAM,WAAW,IAAI,CAAC,QAAQ;gBAC9B,OAAO,WAAW,KAAK,CAAC,QAAQ;YAClC;YAEA,QAAQ,GAAG,CAAC,4CAA4C;YAExD,IAAI,gBAAgB,iBAAiB,OAAO;gBAC1C,OAAO,MAAM,CAAC,UAAU;gBACxB,QAAQ,GAAG,CAAC,qCAAqC;YACnD,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;YAEA,MAAM,SAAS,CAAC,6BAA6B,EAAE,QAAQ;YACvD,QAAQ,GAAG,CAAC,YAAY;YAExB,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,QAAQ,GAAG,CAAC,yCAAyC;YACrD,QAAQ,GAAG,CAAC,0BAA0B,KAAK,YAAY,EAAE,UAAU;YAEnE,IAAI,KAAK,YAAY,IAAI,KAAK,YAAY,CAAC,MAAM,GAAG,GAAG;gBACrD,QAAQ,GAAG,CAAC,4BAA4B,KAAK,YAAY,CAAC,EAAE;YAC9D;YAEA,gBAAgB,KAAK,YAAY,IAAI,EAAE;YACvC,cAAc,CAAA,OAAQ,CAAC;oBACrB,GAAG,IAAI;oBACP,OAAO,KAAK,UAAU,EAAE,SAAS;oBACjC,YAAY,KAAK,UAAU,EAAE,cAAc;gBAC7C,CAAC;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,gBAAgB,EAAE;YAClB,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,qCAAqC;YACrC,MAAM,CAAC,UAAU,YAAY,eAAe,aAAa,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC5E,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;aACP;YAED,MAAM,CAAC,OAAO,SAAS,YAAY,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAChE,SAAS,IAAI;gBACb,WAAW,IAAI;gBACf,cAAc,IAAI;gBAClB,aAAa,IAAI;aAClB;YAED,SAAS;gBACP,OAAO,MAAM,UAAU,EAAE,SAAS;gBAClC,SAAS,QAAQ,UAAU,EAAE,SAAS;gBACtC,YAAY,WAAW,UAAU,EAAE,SAAS;gBAC5C,WAAW,UAAU,UAAU,EAAE,SAAS;YAC5C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,QAAQ,sDAAsD;YAChE,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,6BAA6B,EAAE,eAAe,EAAE;oBAC5E,QAAQ;gBACV;gBACA,IAAI,SAAS,EAAE,EAAE;oBACf;oBACA;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;YAC/C;QACF;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,eAAe;YACnB,SAAS;gBAAE,SAAS;gBAAsB,OAAO;YAAU;YAC3D,UAAU;gBAAE,SAAS;gBAAoB,OAAO;YAAW;YAC3D,UAAU;gBAAE,SAAS;gBAAoB,OAAO;YAAW;YAC3D,UAAU;gBAAE,SAAS;gBAAwB,OAAO;YAAW;YAC/D,eAAe;gBAAE,SAAS;gBAAoB,OAAO;YAAc;YACnE,WAAW;gBAAE,SAAS;gBAAoB,OAAO;YAAY;QAC/D;QAEA,MAAM,SAAS,YAAY,CAAC,OAAoC,IAAI,aAAa,OAAO;QACxF,qBAAO,8OAAC,0HAAA,CAAA,QAAK;YAAC,SAAS,OAAO,OAAO;sBAAG,OAAO,KAAK;;;;;;IACtD;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,cAAc;YAClB,YAAY;YACZ,QAAQ;YACR,YAAY;YACZ,YAAY;YACZ,eAAe;QACjB;QAEA,qBACE,8OAAC;YAAK,WAAW,CAAC,2CAA2C,EAAE,WAAW,CAAC,MAAkC,IAAI,6BAA6B;sBAC3I,MAAM,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;IAG1C;IAEA,MAAM,uBAAuB,aAAa,MAAM,CAAC,CAAA;QAG/C,sBAAsB;QACtB,MAAM,cAAc,iBAAiB,SAAS,IAAI,MAAM,KAAK;QAE7D,oBAAoB;QACpB,MAAM,cAAc,CAAC,cAClB,IAAI,cAAc,IAAI,IAAI,cAAc,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACtF,IAAI,KAAK,IAAI,IAAI,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACpE,IAAI,YAAY,IAAI,IAAI,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAErF,MAAM,SAAS,eAAe;QAE9B,QAAQ,GAAG,CAAC,kBAAkB;YAC5B;YACA;YACA,aAAa;QACf;QAEA,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAKjC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,yHAAA,CAAA,OAAI;;0CACH,8OAAC,yHAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,kNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;0CAE1B,8OAAC,yHAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CAAsB,MAAM,KAAK;;;;;;;;;;;;;;;;;kCAGpD,8OAAC,yHAAA,CAAA,OAAI;;0CACH,8OAAC,yHAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;0CAEnB,8OAAC,yHAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CAAsB,MAAM,OAAO;;;;;;;;;;;;;;;;;kCAGtD,8OAAC,yHAAA,CAAA,OAAI;;0CACH,8OAAC,yHAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;0CAExB,8OAAC,yHAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CAAsB,MAAM,UAAU;;;;;;;;;;;;;;;;;kCAGzD,8OAAC,yHAAA,CAAA,OAAI;;0CACH,8OAAC,yHAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;0CAEzB,8OAAC,yHAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CAAsB,MAAM,SAAS;;;;;;;;;;;;;;;;;;;;;;;0BAM1D,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,8OAAC,yHAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC,0HAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;;;;;;8CAIhB,8OAAC,2HAAA,CAAA,SAAM;oCAAC,OAAO;oCAAc,eAAe;;sDAC1C,8OAAC,2HAAA,CAAA,gBAAa;4CAAC,WAAU;sDACvB,cAAA,8OAAC,2HAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,8OAAC,2HAAA,CAAA,gBAAa;;8DACZ,8OAAC,2HAAA,CAAA,aAAU;oDAAC,OAAM;8DAAM;;;;;;8DACxB,8OAAC,2HAAA,CAAA,aAAU;oDAAC,OAAM;8DAAU;;;;;;8DAC5B,8OAAC,2HAAA,CAAA,aAAU;oDAAC,OAAM;8DAAW;;;;;;8DAC7B,8OAAC,2HAAA,CAAA,aAAU;oDAAC,OAAM;8DAAW;;;;;;8DAC7B,8OAAC,2HAAA,CAAA,aAAU;oDAAC,OAAM;8DAAW;;;;;;8DAC7B,8OAAC,2HAAA,CAAA,aAAU;oDAAC,OAAM;8DAAc;;;;;;8DAChC,8OAAC,2HAAA,CAAA,aAAU;oDAAC,OAAM;8DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUxC,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;;gCAAC;gCAAe,qBAAqB,MAAM;gCAAC;;;;;;;;;;;;kCAExD,8OAAC,yHAAA,CAAA,cAAW;kCACT,wBACC,8OAAC;4BAAI,WAAU;sCAAmB;;;;;mCAChC,aAAa,MAAM,KAAK,kBAC1B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAE;;;;;;8CACH,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;mCAI1C,qBAAqB,MAAM,KAAK,kBAClC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAE;;;;;;8CACH,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;iDAK5C,8OAAC,0HAAA,CAAA,QAAK;;8CACJ,8OAAC,0HAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,0HAAA,CAAA,WAAQ;;0DACP,8OAAC,0HAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,0HAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,0HAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,0HAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,0HAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,0HAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,0HAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;;;;;;;8CAGf,8OAAC,0HAAA,CAAA,YAAS;8CACP,qBAAqB,GAAG,CAAC,CAAC,4BACzB,8OAAC,0HAAA,CAAA,WAAQ;;8DACP,8OAAC,0HAAA,CAAA,YAAS;8DACR,cAAA,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;0EAAe,YAAY,cAAc;;;;;;0EACxD,8OAAC;gEAAI,WAAU;0EAAyB,YAAY,KAAK;;;;;;;;;;;;;;;;;8DAG7D,8OAAC,0HAAA,CAAA,YAAS;8DAAE,YAAY,YAAY,IAAI;;;;;;8DACxC,8OAAC,0HAAA,CAAA,YAAS;8DAAE,qBAAqB,YAAY,aAAa;;;;;;8DAC1D,8OAAC,0HAAA,CAAA,YAAS;8DACR,cAAA,8OAAC;wDAAK,WAAU;kEAAc,YAAY,YAAY;;;;;;;;;;;8DAExD,8OAAC,0HAAA,CAAA,YAAS;8DAAE,eAAe,YAAY,MAAM;;;;;;8DAC7C,8OAAC,0HAAA,CAAA,YAAS;8DAAE,IAAI,KAAK,YAAY,gBAAgB,EAAE,kBAAkB;;;;;;8DACrE,8OAAC,0HAAA,CAAA,YAAS;8DACR,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAM,CAAC,yBAAyB,EAAE,YAAY,cAAc,EAAE;0EAClE,cAAA,8OAAC,2HAAA,CAAA,SAAM;oEAAC,SAAQ;oEAAU,MAAK;8EAC7B,cAAA,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;;;;;;;;;;;0EAGnB,8OAAC,2HAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,aAAa,YAAY,cAAc;0EAEtD,cAAA,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2CA1BX,YAAY,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;YAuCpD,WAAW,UAAU,GAAG,mBACvB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,SAAS,IAAM,cAAc,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,MAAM,KAAK,GAAG,CAAC,GAAG,KAAK,IAAI,GAAG;gCAAG,CAAC;wBACnF,UAAU,WAAW,IAAI,KAAK;kCAC/B;;;;;;kCAGD,8OAAC;wBAAK,WAAU;;4BAAyB;4BACjC,WAAW,IAAI;4BAAC;4BAAK,WAAW,UAAU;;;;;;;kCAElD,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,SAAS,IAAM,cAAc,CAAA,OAAQ,CAAC;oCAAE,GAAG,IAAI;oCAAE,MAAM,KAAK,GAAG,CAAC,KAAK,UAAU,EAAE,KAAK,IAAI,GAAG;gCAAG,CAAC;wBACjG,UAAU,WAAW,IAAI,KAAK,WAAW,UAAU;kCACpD;;;;;;;;;;;;;;;;;;AAOX"}}, {"offset": {"line": 1497, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}