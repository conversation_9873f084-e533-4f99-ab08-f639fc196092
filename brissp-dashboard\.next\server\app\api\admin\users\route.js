const CHUNK_PUBLIC_PATH = "server/app/api/admin/users/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_next_997bc5._.js");
runtime.loadChunk("server/chunks/node_modules_mysql2_6f8e1c._.js");
runtime.loadChunk("server/chunks/node_modules_iconv-lite_1d546d._.js");
runtime.loadChunk("server/chunks/node_modules_aws-ssl-profiles_lib_a90e16._.js");
runtime.loadChunk("server/chunks/node_modules_e14bb3._.js");
runtime.loadChunk("server/chunks/[root of the server]__eb4f16._.js");
runtime.loadChunk("server/chunks/_c61fa3._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/admin/users/route/actions.js [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/app/api/admin/users/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
