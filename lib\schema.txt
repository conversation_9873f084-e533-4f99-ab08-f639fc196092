-- Create the courses table (unchanged)
CREATE TABLE courses (
    course_id INT PRIMARY KEY AUTO_INCREMENT,
    course_code VARCHAR(20) NOT NULL UNIQUE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    duration_months INT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    department VARCHAR(100) NOT NULL,
    category ENUM('adults', 'kids') NOT NULL,
    image_url VARCHAR(255),
    program_type VARCHAR(100) NOT NULL,
    num_lectures INT NOT NULL,
    skill_level ENUM('beginner', 'intermediate', 'advanced') NOT NULL,
    languages VARCHAR(255) NOT NULL,
    class_days VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create the curriculum table (unchanged)
CREATE TABLE curriculum (
    curriculum_id INT PRIMARY KEY AUTO_INCREMENT,
    course_id INT NOT NULL,
    week_number INT NOT NULL,
    topic VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    learning_objectives TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE,
    UNIQUE KEY unique_week_per_course (course_id, week_number)
);

-- Create the gallery table (unchanged)
CREATE TABLE gallery (
    gallery_id INT PRIMARY KEY AUTO_INCREMENT,
    course_id INT NOT NULL,
    image_url VARCHAR(255) NOT NULL,
    image_title VARCHAR(100),
    image_description TEXT,
    image_type ENUM('cover', 'banner', 'content', 'thumbnail') NOT NULL,
    display_order INT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE,
    INDEX idx_course_images (course_id, is_active)
);

-- Create the notice_board table
CREATE TABLE notice_board (
    notice_id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    author VARCHAR(100),
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    publish_date DATE NOT NULL,
    expiry_date DATE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active_notices (is_active, publish_date, expiry_date)
);

-- Create the downloadable_files table
CREATE TABLE downloadable_files (
  file_id INT AUTO_INCREMENT PRIMARY KEY,
  file_name VARCHAR(255) NOT NULL,
  file_url VARCHAR(512) NOT NULL,
  file_type VARCHAR(100),
  file_size INT,
  description TEXT,
  course_id INT,
  upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE
);


-- Create graduates table
CREATE TABLE graduates (
    graduate_id INT PRIMARY KEY AUTO_INCREMENT,
    course_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    cell_number VARCHAR(20),
    year_of_completion YEAR NOT NULL,
    period_of_study VARCHAR(50) NOT NULL,
    graduate_image_url VARCHAR(205) NOT NULL,
    final_score DECIMAL(5,2),
    certificate_number VARCHAR(50) UNIQUE NOT NULL,
    certificate_file_url VARCHAR(191),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(course_id),
    INDEX idx_year_completion (year_of_completion)
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create projects table
CREATE TABLE projects (
    project_id INT PRIMARY KEY AUTO_INCREMENT,
    graduate_id INT NOT NULL,
    project_title VARCHAR(100) NOT NULL,
    description TEXT,
    project_url VARCHAR(191),
    github_url VARCHAR(191),
    technologies_used TEXT,
    completion_date DATE,
    is_featured BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (graduate_id) REFERENCES graduates(graduate_id),
    INDEX idx_featured (is_featured)
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create social links table
CREATE TABLE social_links (
    link_id INT PRIMARY KEY AUTO_INCREMENT,
    graduate_id INT NOT NULL,
    platform ENUM('github', 'linkedin', 'twitter', 'portfolio', 'other') NOT NULL,
    url VARCHAR(191) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (graduate_id) REFERENCES graduates(graduate_id),
    UNIQUE KEY unique_graduate_platform (graduate_id, platform)
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create applications table
CREATE TABLE applications (
    application_id INT PRIMARY KEY AUTO_INCREMENT,
    course_id INT NOT NULL,
    student_type ENUM('child', 'adult') NOT NULL,
    study_mode VARCHAR(50) NOT NULL,
    
    -- Personal Information
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    other_names VARCHAR(100),
    gender ENUM('male', 'female', 'other') NOT NULL,
    
    -- Additional Information
    marital_status ENUM('single', 'married', 'divorced', 'widowed'),
    date_of_birth DATE NOT NULL,
    nationality VARCHAR(100) NOT NULL,
    id_number VARCHAR(100) NOT NULL,
    
    -- Academic Information
    academic_year VARCHAR(20) NOT NULL,
    intake ENUM('january', 'may', 'september') NOT NULL,
    
    -- Contact Information
    email VARCHAR(255) NOT NULL,
    phone_number VARCHAR(50) NOT NULL,
    country VARCHAR(100) NOT NULL,
    
    -- Application Status
    status ENUM('pending', 'approved', 'rejected', 'waitlisted') DEFAULT 'pending',
    application_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reviewed_by INT,
    review_date TIMESTAMP NULL,
    review_notes TEXT,

    -- Indexes and Foreign Keys
    FOREIGN KEY (course_id) REFERENCES courses(course_id),
    INDEX idx_status (status),
    INDEX idx_application_date (application_date),
    INDEX idx_email (email)
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create the course_resources table
CREATE TABLE course_resources (
    resource_id INT PRIMARY KEY AUTO_INCREMENT,
    course_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    file_url VARCHAR(255) NOT NULL COMMENT 'URL of the file hosted on Cloudinary',
    file_type ENUM('video', 'document', 'audio', 'other') NOT NULL COMMENT 'Type of resource',
    file_size INT COMMENT 'Size of the file in bytes',
    description TEXT COMMENT 'Optional description of the resource',
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Date when the resource was uploaded',
    is_public BOOLEAN DEFAULT false COMMENT 'Whether the resource is publicly accessible',
    is_active BOOLEAN DEFAULT true COMMENT 'Whether the resource is currently active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE,
    INDEX idx_course_resources (course_id, is_active),
    INDEX idx_public_resources (is_public, is_active)
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;


CREATE TABLE users (
    user_id INT PRIMARY KEY AUTO_INCREMENT,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL COMMENT 'Hashed password',
    is_active BOOLEAN DEFAULT true COMMENT 'Account status',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email)
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;


CREATE TABLE user_course_enrollments (
    enrollment_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    course_id INT NOT NULL,
    enrollment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Date of enrollment',
    status ENUM('active', 'completed', 'dropped') DEFAULT 'active' COMMENT 'Enrollment status',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE,
    UNIQUE KEY unique_enrollment (user_id, course_id) COMMENT 'Prevents duplicate enrollments',
    INDEX idx_user_enrollments (user_id),
    INDEX idx_course_enrollments (course_id)
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create the results table
CREATE TABLE results (
    result_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    course_id INT NOT NULL,
    enrollment_id INT NOT NULL COMMENT 'Links to the specific enrollment record',
    assessment_type ENUM('quiz', 'exam', 'assignment', 'project', 'final') NOT NULL COMMENT 'Type of assessment',
    assessment_title VARCHAR(255) NOT NULL COMMENT 'Name or title of the assessment',
    score DECIMAL(5,2) NOT NULL COMMENT 'Score achieved (e.g., out of 100)',
    max_score DECIMAL(5,2) NOT NULL DEFAULT 100.00 COMMENT 'Maximum possible score',
    result_date DATE NOT NULL COMMENT 'Date the result was recorded',
    comments TEXT COMMENT 'Optional feedback or notes',
    is_passed BOOLEAN DEFAULT false COMMENT 'Indicates if the user passed the assessment',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(course_id) ON DELETE CASCADE,
    FOREIGN KEY (enrollment_id) REFERENCES user_course_enrollments(enrollment_id) ON DELETE CASCADE,
    INDEX idx_user_results (user_id, course_id),
    INDEX idx_enrollment_results (enrollment_id),
    INDEX idx_result_date (result_date)
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;


-- Create companies table for internship providers
CREATE TABLE companies (
    company_id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    logo_url VARCHAR(255),
    website VARCHAR(255),
    location VARCHAR(255),
    industry VARCHAR(100),
    size ENUM('startup', 'small', 'medium', 'large', 'enterprise') DEFAULT 'medium',
    contact_email VARCHAR(255),
    contact_phone VARCHAR(50),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_industry (industry),
    INDEX idx_location (location),
    INDEX idx_active (is_active)
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create internships table
CREATE TABLE internships (
    internship_id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    company_id INT NOT NULL,
    type ENUM('paid', 'unpaid') NOT NULL,
    duration_months INT NOT NULL,
    requirements TEXT,
    responsibilities TEXT,
    industry VARCHAR(100) NOT NULL,
    skills_required TEXT,
    application_deadline DATE,
    start_date DATE,
    stipend_amount DECIMAL(10,2) NULL COMMENT 'Monthly stipend for paid internships',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(company_id) ON DELETE CASCADE,
    INDEX idx_type (type),
    INDEX idx_industry (industry),
    INDEX idx_active (is_active),
    INDEX idx_deadline (application_deadline),
    INDEX idx_start_date (start_date)
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create internship applications table
CREATE TABLE internship_applications (
    application_id INT PRIMARY KEY AUTO_INCREMENT,
    internship_id INT NULL COMMENT 'NULL for general applications, specific ID for targeted applications',
    student_name VARCHAR(255) NOT NULL,
    student_email VARCHAR(255) NOT NULL,
    student_phone VARCHAR(50) NOT NULL,
    university VARCHAR(255) NOT NULL,
    course_of_study VARCHAR(255) NOT NULL,
    year_of_study VARCHAR(20) NOT NULL,
    gpa VARCHAR(20),
    cv_url VARCHAR(500),
    cover_letter TEXT,
    portfolio_url VARCHAR(500),
    linkedin_url VARCHAR(500),
    github_url VARCHAR(500),
    availability_start DATE,
    preferred_duration VARCHAR(50),
    motivation TEXT,
    relevant_experience TEXT,
    technical_skills TEXT,
    soft_skills TEXT,
    preferred_industry VARCHAR(100) COMMENT 'For general applications',
    internship_type_preference ENUM('paid', 'unpaid', 'both') COMMENT 'For general applications',
    status ENUM('pending', 'reviewed', 'shortlisted', 'accepted', 'rejected') DEFAULT 'pending',
    application_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reviewed_by INT,
    review_date TIMESTAMP NULL,
    review_notes TEXT,
    FOREIGN KEY (internship_id) REFERENCES internships(internship_id) ON DELETE CASCADE,
    INDEX idx_status (status),
    INDEX idx_application_date (application_date),
    INDEX idx_student_email (student_email),
    INDEX idx_internship (internship_id),
    UNIQUE KEY unique_application (internship_id, student_email) COMMENT 'Prevents duplicate applications'
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Insert sample companies
INSERT INTO companies (name, description, logo_url, website, location, industry, size, contact_email, contact_phone) VALUES
('TechZambia Solutions', 'Leading software development company specializing in web and mobile applications for African markets.', 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?auto=format&fit=crop&w=100&q=80', 'https://techzambia.com', 'Lusaka, Zambia', 'Technology', 'medium', '<EMAIL>', '+260 **********'),
('DataFlow Analytics', 'Data science and analytics company helping businesses make data-driven decisions.', 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?auto=format&fit=crop&w=100&q=80', 'https://dataflow.zm', 'Lusaka, Zambia', 'Data Science', 'small', '<EMAIL>', '+260 **********'),
('CyberGuard Zambia', 'Cybersecurity firm providing security solutions for enterprises across Southern Africa.', 'https://images.unsplash.com/photo-**********-4bd374c3f58b?auto=format&fit=crop&w=100&q=80', 'https://cyberguard.zm', 'Lusaka, Zambia', 'Cybersecurity', 'medium', '<EMAIL>', '+260 **********'),
('CloudTech Africa', 'Cloud computing and DevOps services provider for African businesses.', 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?auto=format&fit=crop&w=100&q=80', 'https://cloudtech.africa', 'Lusaka, Zambia', 'Cloud Computing', 'large', '<EMAIL>', '+260 **********'),
('MobileFirst Innovations', 'Mobile app development studio creating innovative solutions for various industries.', 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?auto=format&fit=crop&w=100&q=80', 'https://mobilefirst.zm', 'Lusaka, Zambia', 'Mobile Development', 'startup', '<EMAIL>', '+260 **********');

-- Insert sample internships
INSERT INTO internships (title, description, company_id, type, duration_months, requirements, responsibilities, industry, skills_required, application_deadline, start_date, stipend_amount) VALUES
('Software Development Intern', 'Join our development team to work on exciting web and mobile applications. You will gain hands-on experience with modern technologies and contribute to real projects.', 1, 'paid', 6, 'Currently enrolled in Computer Science or related field\nBasic knowledge of programming\nStrong problem-solving skills', 'Assist in developing web applications\nWrite clean, maintainable code\nParticipate in code reviews\nCollaborate with senior developers', 'Technology', 'JavaScript, Python, React, Node.js', '2024-02-15', '2024-03-01', 2500.00),
('Data Science Intern', 'Work with our data science team to analyze large datasets and build predictive models. Perfect opportunity to apply your statistical knowledge in real-world scenarios.', 2, 'paid', 4, 'Statistics or Data Science background\nPython programming experience\nFamiliarity with data analysis libraries', 'Analyze business data\nBuild predictive models\nCreate data visualizations\nPresent findings to stakeholders', 'Data Science', 'Python, R, SQL, Pandas, Scikit-learn, Tableau', '2024-02-20', '2024-03-15', 2000.00),
('Cybersecurity Intern', 'Learn about cybersecurity practices and help protect our clients from digital threats. Gain experience in penetration testing and security auditing.', 3, 'unpaid', 3, 'Interest in cybersecurity\nBasic networking knowledge\nEthical mindset and attention to detail', 'Assist in security assessments\nMonitor security systems\nDocument security procedures\nSupport incident response', 'Cybersecurity', 'Network Security, Ethical Hacking, Risk Assessment', '2024-02-10', '2024-03-01', NULL),
('Cloud Engineering Intern', 'Get hands-on experience with cloud platforms and DevOps practices. Work on infrastructure automation and deployment pipelines.', 4, 'paid', 6, 'Basic understanding of cloud concepts\nLinux command line experience\nInterest in automation', 'Deploy applications to cloud\nManage cloud infrastructure\nAutomate deployment processes\nMonitor system performance', 'Cloud Computing', 'AWS, Docker, Kubernetes, Linux, Python', '2024-02-25', '2024-04-01', 3000.00),
('Mobile App Development Intern', 'Create innovative mobile applications for iOS and Android platforms. Work with cutting-edge technologies and user experience design.', 5, 'paid', 4, 'Mobile development interest\nBasic programming knowledge\nCreative problem-solving skills', 'Develop mobile applications\nImplement user interfaces\nTest app functionality\nCollaborate with design team', 'Mobile Development', 'React Native, Flutter, Swift, Kotlin', '2024-02-12', '2024-03-10', 1800.00),
('UI/UX Design Intern', 'Design user-friendly interfaces and improve user experience across our digital products. Learn industry-standard design tools and methodologies.', 1, 'unpaid', 3, 'Design portfolio or coursework\nCreativity and attention to detail\nBasic design software knowledge', 'Create user interface designs\nConduct user research\nDevelop wireframes and prototypes\nCollaborate with development team', 'Design', 'Figma, Adobe XD, Sketch, User Research', '2024-02-18', '2024-03-20', NULL);


-- Insert sample refresher courses
INSERT INTO courses (
  course_code, title, description, duration_months, price, department,
  category, image_url, program_type, num_lectures, skill_level, languages, class_days
) VALUES 
(
  'REF-DS-001',
  'Data Science Bootcamp',
  'Comprehensive refresher course covering modern data science techniques, machine learning algorithms, and practical applications using Python, R, and popular frameworks. Perfect for professionals looking to update their skills in the rapidly evolving field of data science.',
  6,
  1200.00,
  'Computer Science',
  'adults',
  'https://images.unsplash.com/photo-1551288049-bebda4e38f71?auto=format&fit=crop&w=800&q=80',
  'refresher',
  48,
  'intermediate',
  'English',
  'Evenings & Weekends'
),
(
  'REF-CYB-001',
  'Cybersecurity Fundamentals & Practice',
  'Hands-on cybersecurity refresher course covering network security, ethical hacking, incident response, and compliance frameworks. Designed for IT professionals seeking to strengthen their security expertise and stay current with emerging threats.',
  4,
  950.00,
  'Computer Science',
  'adults',
  'https://images.unsplash.com/photo-**********-4bd374c3f58b?auto=format&fit=crop&w=800&q=80',
  'refresher',
  32,
  'intermediate',
  'English',
  'Evenings & Weekends'
),
(
  'REF-NET-001',
  'Networking Essentials (Cisco-based)',
  'Cisco-certified networking refresher course covering routing, switching, network troubleshooting, and modern network architectures. Ideal for network administrators and engineers looking to update their Cisco skills and prepare for certification.',
  5,
  1100.00,
  'Computer Science',
  'adults',
  'https://images.unsplash.com/photo-**********-ef010cbdcc31?auto=format&fit=crop&w=800&q=80',
  'refresher',
  40,
  'intermediate',
  'English',
  'Evenings & Weekends'
),
(
  'REF-WEB-001',
  'Web & Mobile App Development (Frontend & Backend)',
  'Full-stack development refresher covering modern JavaScript frameworks (React, Node.js), mobile development (React Native), and cloud deployment. Perfect for developers wanting to update their skills with current technologies and best practices.',
  8,
  1500.00,
  'Computer Science',
  'adults',
  'https://images.unsplash.com/photo-*************-dccba630e2f6?auto=format&fit=crop&w=800&q=80',
  'refresher',
  64,
  'intermediate',
  'English',
  'Evenings & Weekends'
),
(
  'REF-ML-001',
  'Machine Learning & AI for Beginners',
  'Beginner-friendly machine learning refresher course covering fundamental algorithms, neural networks, and practical AI applications. Designed for professionals new to ML/AI or those seeking to refresh their understanding of core concepts.',
  6,
  1300.00,
  'Computer Science',
  'adults',
  'https://images.unsplash.com/photo-1555949963-aa79dcee981c?auto=format&fit=crop&w=800&q=80',
  'refresher',
  48,
  'beginner',
  'English',
  'Evenings & Weekends'
),
(
  'REF-CLOUD-001',
  'Cloud Computing & DevOps Intro',
  'Introduction to cloud computing and DevOps practices covering AWS/Azure, containerization (Docker), CI/CD pipelines, and infrastructure as code. Perfect for IT professionals transitioning to cloud-based environments.',
  5,
  1150.00,
  'Computer Science',
  'adults',
  'https://images.unsplash.com/photo-1451187580459-43490279c0fa?auto=format&fit=crop&w=800&q=80',
  'refresher',
  40,
  'beginner',
  'English',
  'Evenings & Weekends'
);

-- Insert sample curriculum for Data Science Bootcamp
INSERT INTO curriculum (course_id, week_number, topic, content, learning_objectives) VALUES
((SELECT course_id FROM courses WHERE course_code = 'REF-DS-001'), 1, 'Python for Data Science Refresher', 'Review of Python fundamentals, NumPy, Pandas, and data manipulation techniques', 'Refresh Python skills and master data manipulation libraries'),
((SELECT course_id FROM courses WHERE course_code = 'REF-DS-001'), 2, 'Data Visualization & Exploratory Analysis', 'Matplotlib, Seaborn, and Plotly for creating compelling visualizations', 'Create professional data visualizations and perform exploratory data analysis'),
((SELECT course_id FROM courses WHERE course_code = 'REF-DS-001'), 3, 'Statistical Analysis & Hypothesis Testing', 'Statistical concepts, hypothesis testing, and A/B testing methodologies', 'Apply statistical methods to real-world data problems'),
((SELECT course_id FROM courses WHERE course_code = 'REF-DS-001'), 4, 'Machine Learning Fundamentals', 'Supervised and unsupervised learning algorithms using Scikit-learn', 'Implement and evaluate machine learning models'),
((SELECT course_id FROM courses WHERE course_code = 'REF-DS-001'), 5, 'Advanced ML & Model Deployment', 'Deep learning basics, model optimization, and deployment strategies', 'Deploy machine learning models to production environments'),
((SELECT course_id FROM courses WHERE course_code = 'REF-DS-001'), 6, 'Capstone Project', 'End-to-end data science project from data collection to model deployment', 'Complete a comprehensive data science project portfolio piece');

-- Insert sample curriculum for Cybersecurity course
INSERT INTO curriculum (course_id, week_number, topic, content, learning_objectives) VALUES
((SELECT course_id FROM courses WHERE course_code = 'REF-CYB-001'), 1, 'Security Fundamentals Review', 'CIA triad, threat landscape, and security frameworks overview', 'Understand core security principles and current threat environment'),
((SELECT course_id FROM courses WHERE course_code = 'REF-CYB-001'), 2, 'Network Security & Firewalls', 'Network security protocols, firewall configuration, and intrusion detection', 'Configure and manage network security infrastructure'),
((SELECT course_id FROM courses WHERE course_code = 'REF-CYB-001'), 3, 'Ethical Hacking & Penetration Testing', 'Hands-on penetration testing tools and methodologies', 'Perform ethical hacking assessments and vulnerability testing'),
((SELECT course_id FROM courses WHERE course_code = 'REF-CYB-001'), 4, 'Incident Response & Forensics', 'Incident response procedures, digital forensics, and recovery strategies', 'Develop incident response plans and conduct forensic analysis');

-- Insert sample curriculum for Web Development course
INSERT INTO curriculum (course_id, week_number, topic, content, learning_objectives) VALUES
((SELECT course_id FROM courses WHERE course_code = 'REF-WEB-001'), 1, 'Modern JavaScript & ES6+', 'Latest JavaScript features, async/await, and modern development practices', 'Master modern JavaScript development techniques'),
((SELECT course_id FROM courses WHERE course_code = 'REF-WEB-001'), 2, 'React.js Fundamentals', 'Component-based architecture, hooks, and state management', 'Build dynamic user interfaces with React'),
((SELECT course_id FROM courses WHERE course_code = 'REF-WEB-001'), 3, 'Node.js & Express Backend', 'Server-side development, RESTful APIs, and database integration', 'Develop robust backend applications'),
((SELECT course_id FROM courses WHERE course_code = 'REF-WEB-001'), 4, 'Database Design & Integration', 'SQL and NoSQL databases, ORM/ODM, and data modeling', 'Design and implement efficient database solutions'),
((SELECT course_id FROM courses WHERE course_code = 'REF-WEB-001'), 5, 'React Native Mobile Development', 'Cross-platform mobile app development with React Native', 'Create mobile applications for iOS and Android'),
((SELECT course_id FROM courses WHERE course_code = 'REF-WEB-001'), 6, 'Testing & Quality Assurance', 'Unit testing, integration testing, and code quality tools', 'Implement comprehensive testing strategies'),
((SELECT course_id FROM courses WHERE course_code = 'REF-WEB-001'), 7, 'Deployment & DevOps', 'CI/CD pipelines, containerization, and cloud deployment', 'Deploy applications to production environments'),
((SELECT course_id FROM courses WHERE course_code = 'REF-WEB-001'), 8, 'Full-Stack Project', 'Complete full-stack application with modern best practices', 'Build and deploy a professional full-stack application');

-- Create pitch deck applications table
CREATE TABLE pitch_deck_applications (
    application_id INT PRIMARY KEY AUTO_INCREMENT,
    applicant_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50) NOT NULL,
    company_name VARCHAR(255),
    industry VARCHAR(100),
    funding_stage ENUM('pre-seed', 'seed', 'series-a', 'series-b', 'later-stage') NOT NULL,
    funding_amount VARCHAR(100),
    business_description TEXT NOT NULL,
    target_audience TEXT,
    current_traction TEXT,
    team_size INT,
    previous_funding BOOLEAN DEFAULT false,
    pitch_deadline DATE,
    specific_requirements TEXT,
    preferred_start_date DATE,
    budget_range ENUM('basic', 'standard', 'premium', 'enterprise') NOT NULL,
    referral_source VARCHAR(100),
    status ENUM('pending', 'reviewed', 'accepted', 'rejected', 'in-progress', 'completed') DEFAULT 'pending',
    application_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reviewed_by INT,
    review_date TIMESTAMP NULL,
    review_notes TEXT,
    assigned_consultant VARCHAR(255),
    project_start_date DATE,
    project_completion_date DATE,
    final_pitch_deck_url VARCHAR(500),
    success_metrics TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_application_date (application_date),
    INDEX idx_email (email),
    INDEX idx_funding_stage (funding_stage)
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create final year project applications table
CREATE TABLE final_year_project_applications (
    application_id INT PRIMARY KEY AUTO_INCREMENT,
    student_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50) NOT NULL,
    university VARCHAR(255) NOT NULL,
    course_of_study VARCHAR(255) NOT NULL,
    year_of_study VARCHAR(20) NOT NULL,
    student_id VARCHAR(100),
    supervisor_name VARCHAR(255),
    supervisor_email VARCHAR(255),
    project_title VARCHAR(500),
    project_description TEXT,
    project_type ENUM('research', 'development', 'analysis', 'design', 'other') NOT NULL,
    research_area VARCHAR(255),
    methodology TEXT,
    expected_outcomes TEXT,
    timeline_weeks INT,
    required_resources TEXT,
    technical_requirements TEXT,
    preferred_supervisor_expertise VARCHAR(255),
    project_deadline DATE,
    defense_date DATE,
    university_requirements TEXT,
    additional_notes TEXT,
    status ENUM('pending', 'reviewed', 'accepted', 'rejected', 'in-progress', 'completed') DEFAULT 'pending',
    application_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reviewed_by INT,
    review_date TIMESTAMP NULL,
    review_notes TEXT,
    assigned_supervisor VARCHAR(255),
    project_start_date DATE,
    project_completion_date DATE,
    final_report_url VARCHAR(500),
    presentation_url VARCHAR(500),
    grade_achieved VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_application_date (application_date),
    INDEX idx_email (email),
    INDEX idx_university (university),
    INDEX idx_project_type (project_type)
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create internship applications table
CREATE TABLE internship_applications (
    application_id INT PRIMARY KEY AUTO_INCREMENT,
    applicant_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50) NOT NULL,
    date_of_birth DATE,
    gender ENUM('male', 'female', 'other', 'prefer-not-to-say'),
    nationality VARCHAR(100),
    address TEXT,
    city VARCHAR(100),
    province VARCHAR(100),
    postal_code VARCHAR(20),
    emergency_contact_name VARCHAR(255),
    emergency_contact_phone VARCHAR(50),
    emergency_contact_relationship VARCHAR(100),
    education_level ENUM('high-school', 'diploma', 'bachelors', 'masters', 'phd', 'other') NOT NULL,
    institution_name VARCHAR(255),
    field_of_study VARCHAR(255),
    graduation_year YEAR,
    current_gpa DECIMAL(3,2),
    relevant_coursework TEXT,
    programming_languages TEXT,
    technical_skills TEXT,
    soft_skills TEXT,
    previous_experience TEXT,
    portfolio_url VARCHAR(500),
    github_url VARCHAR(500),
    linkedin_url VARCHAR(500),
    cv_file_url VARCHAR(500),
    cover_letter TEXT,
    internship_type ENUM('web-development', 'mobile-development', 'data-science', 'ui-ux-design', 'digital-marketing', 'business-analysis', 'other') NOT NULL,
    preferred_duration ENUM('3-months', '6-months', '12-months', 'flexible') NOT NULL,
    preferred_start_date DATE,
    availability ENUM('full-time', 'part-time', 'flexible') NOT NULL,
    remote_preference ENUM('remote', 'on-site', 'hybrid', 'no-preference') NOT NULL,
    salary_expectation VARCHAR(100),
    transport_availability BOOLEAN DEFAULT false,
    willing_to_relocate BOOLEAN DEFAULT false,
    career_goals TEXT,
    why_briisp TEXT,
    additional_info TEXT,
    referral_source VARCHAR(100),
    status ENUM('pending', 'reviewed', 'interview-scheduled', 'interviewed', 'accepted', 'rejected', 'withdrawn') DEFAULT 'pending',
    application_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reviewed_by INT,
    review_date TIMESTAMP NULL,
    review_notes TEXT,
    interview_date DATETIME NULL,
    interview_notes TEXT,
    interviewer VARCHAR(255),
    internship_start_date DATE,
    internship_end_date DATE,
    supervisor_assigned VARCHAR(255),
    department_assigned VARCHAR(100),
    final_evaluation_score DECIMAL(3,2),
    completion_certificate_url VARCHAR(500),
    recommendation_letter_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_application_date (application_date),
    INDEX idx_email (email),
    INDEX idx_internship_type (internship_type),
    INDEX idx_education_level (education_level),
    INDEX idx_preferred_start_date (preferred_start_date)
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create pitch deck progress tracking table
CREATE TABLE pitch_deck_progress (
    progress_id INT PRIMARY KEY AUTO_INCREMENT,
    application_id INT NOT NULL,
    milestone ENUM('initial-consultation', 'market-research', 'deck-outline', 'content-creation', 'design-phase', 'review-feedback', 'final-delivery') NOT NULL,
    status ENUM('pending', 'in-progress', 'completed', 'on-hold') DEFAULT 'pending',
    start_date DATE,
    completion_date DATE,
    notes TEXT,
    deliverables_url VARCHAR(500),
    feedback TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (application_id) REFERENCES pitch_deck_applications(application_id) ON DELETE CASCADE,
    INDEX idx_application_milestone (application_id, milestone),
    INDEX idx_status (status)
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create final year project progress tracking table
CREATE TABLE fyp_progress (
    progress_id INT PRIMARY KEY AUTO_INCREMENT,
    application_id INT NOT NULL,
    milestone ENUM('proposal-review', 'literature-review', 'methodology-design', 'data-collection', 'analysis-phase', 'draft-writing', 'final-submission', 'defense-preparation') NOT NULL,
    status ENUM('pending', 'in-progress', 'completed', 'on-hold') DEFAULT 'pending',
    start_date DATE,
    completion_date DATE,
    notes TEXT,
    deliverables_url VARCHAR(500),
    supervisor_feedback TEXT,
    grade DECIMAL(3,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (application_id) REFERENCES final_year_project_applications(application_id) ON DELETE CASCADE,
    INDEX idx_application_milestone (application_id, milestone),
    INDEX idx_status (status)
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create internship progress tracking table
CREATE TABLE internship_progress (
    progress_id INT PRIMARY KEY AUTO_INCREMENT,
    application_id INT NOT NULL,
    week_number INT NOT NULL,
    learning_objectives TEXT,
    tasks_completed TEXT,
    skills_developed TEXT,
    challenges_faced TEXT,
    supervisor_feedback TEXT,
    self_assessment_score DECIMAL(3,2),
    supervisor_score DECIMAL(3,2),
    attendance_days INT,
    total_working_days INT,
    project_contributions TEXT,
    week_start_date DATE,
    week_end_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (application_id) REFERENCES internship_applications(application_id) ON DELETE CASCADE,
    INDEX idx_application_week (application_id, week_number),
    INDEX idx_week_dates (week_start_date, week_end_date)
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
