/* eslint-disable react/no-unescaped-entities */
"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Course } from "@/lib/types";
import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";

interface CourseFormProps {
  initialData?: Course;
  onSubmit: (data: Partial<Course>, imageFile?: File) => void;
  isLoading: boolean;
}

export default function CourseForm({
  initialData,
  onSubmit,
  isLoading,
}: CourseFormProps) {
  const [formData, setFormData] = useState<Partial<Course>>(
    initialData || {
      title: "",
      course_code: "",
      description: "",
      duration_months: 0,
      price: 0,
      department: "",
      category: "adults",
      program_type: "",
      num_lectures: 0,
      skill_level: "beginner",
      languages: "",
      class_days: "",
    }
  );

  const [imageFile, setImageFile] = useState<File | null>(null);

  const editor = useEditor({
    extensions: [StarterKit],
    content: formData.description,
    onUpdate: ({ editor }) => {
      setFormData((prev) => ({
        ...prev,
        description: editor.getHTML(),
      }));
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData, imageFile || undefined);
  };

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setImageFile(file);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium mb-2">Course Title</label>
          <Input
            name="title"
            value={formData.title}
            onChange={handleChange}
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-2">Course Code</label>
          <Input
            name="course_code"
            value={formData.course_code}
            onChange={handleChange}
            required
          />
        </div>
        <div className="col-span-2">
          <label className="block text-sm font-medium mb-2">Description</label>
          <div className="border rounded-md p-2">
            <div className="border-b p-2 mb-2 flex gap-2">
              <button
                type="button"
                onClick={() => editor?.chain().focus().toggleBold().run()}
                className={`p-2 ${
                  editor?.isActive("bold") ? "bg-gray-200" : ""
                }`}
              >
                Bold
              </button>
              <button
                type="button"
                onClick={() => editor?.chain().focus().toggleItalic().run()}
                className={`p-2 ${
                  editor?.isActive("italic") ? "bg-gray-200" : ""
                }`}
              >
                Italic
              </button>
              <button
                type="button"
                onClick={() => editor?.chain().focus().toggleBulletList().run()}
                className={`p-2 ${
                  editor?.isActive("bulletList") ? "bg-gray-200" : ""
                }`}
              >
                Bullet List
              </button>
            </div>
            <EditorContent
              editor={editor}
              className="min-h-[200px] prose max-w-none"
            />
          </div>
        </div>
        <div>
          <label className="block text-sm font-medium mb-2">
            Duration (months)
          </label>
          <Input
            type="number"
            name="duration_months"
            value={formData.duration_months}
            onChange={handleChange}
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-2">Price</label>
          <Input
            type="number"
            name="price"
            value={formData.price}
            onChange={handleChange}
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-2">Department</label>
          <select    
           title="department"
            name="department"
            value={formData.department}
            onChange={handleChange}
            className="w-full border rounded-md p-2"
            required
          >
            <option value="beginner">Auto Engineering</option>
            <option value="intermediate">Computer Science</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium mb-2">Category</label>
          <select
            title="category"
            name="category"
            value={formData.category}
            onChange={handleChange}
            className="w-full border rounded-md p-2"
            required
          >
            <option value="adults">Adults</option>
            <option value="kids">Kids</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium mb-2">Program Type</label>
          <select
            title="program_type"
            name="program_type"
            value={formData.program_type}
            onChange={handleChange}
            className="w-full border rounded-md p-2"
            required
          >
            <option value="">Select program type</option>
            <option value="fulltime">Fulltime</option>
            <option value="adults-online">Adults online</option>
            <option value="kids-online">Kids online</option>
            <option value="distance">Distance</option>
            <option value="part-time">Part time</option>
            <option value="weekend">Weekend</option>
          </select>
          <p className="text-xs text-gray-500 mt-1">
            For refresher courses, use the dedicated "Add Refresher Course" button
          </p>
        </div>
        <div>
          <label className="block text-sm font-medium mb-2">
            Number of Lectures
          </label>
          <Input
            type="number"
            name="num_lectures"
            value={formData.num_lectures}
            onChange={handleChange}
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-2">Skill Level</label>
          <select
            title="skill_level"
            name="skill_level"
            value={formData.skill_level}
            onChange={handleChange}
            className="w-full border rounded-md p-2"
            required
          >
            <option value="all">All</option>
            <option value="beginner">Beginner</option>
            <option value="intermediate">Intermediate</option>
            <option value="advanced">Advanced</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium mb-2">Languages</label>
          <Input
            name="languages"
            value={formData.languages}
            onChange={handleChange}
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-2">Class Days</label>
          <Input
            name="class_days"
            value={formData.class_days}
            onChange={handleChange}
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-2">Upload Image</label>
          <Input type="file" accept="image/*" onChange={handleFileChange} />
        </div>
      </div>
      <div className="flex justify-end space-x-4">
        <Button type="submit" disabled={isLoading}>
          {isLoading ? "Saving..." : "Save Course"}
        </Button>
      </div>
    </form>
  );
}
