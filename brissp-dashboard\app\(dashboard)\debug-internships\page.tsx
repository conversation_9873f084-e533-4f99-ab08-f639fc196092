"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function DebugInternshipsPage() {
  const [testResult, setTestResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const runTest = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/test-internships')
      const data = await response.json()
      setTestResult(data)
    } catch (error) {
      console.error('Test failed:', error)
      setTestResult({ error: 'Test failed', details: error })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    runTest()
  }, [])

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Internship Applications Debug</h1>
          <p className="text-gray-600">Test database connection and data</p>
        </div>
        <Button onClick={runTest} disabled={loading}>
          {loading ? 'Testing...' : 'Run Test'}
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Database Test Results</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div>Running database tests...</div>
          ) : testResult ? (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <strong>Table Exists:</strong> {testResult.tableExists ? 'Yes' : 'No'}
                </div>
                <div>
                  <strong>Total Records:</strong> {testResult.totalRecords || 0}
                </div>
              </div>

              {testResult.error && (
                <div className="bg-red-50 border border-red-200 rounded p-4">
                  <strong>Error:</strong> {testResult.error}
                  {testResult.details && (
                    <div className="mt-2 text-sm">
                      <strong>Details:</strong> {testResult.details}
                    </div>
                  )}
                </div>
              )}

              {testResult.tableStructure && (
                <div>
                  <h3 className="font-semibold mb-2">Table Structure:</h3>
                  <div className="bg-gray-50 p-4 rounded text-sm">
                    <pre>{JSON.stringify(testResult.tableStructure, null, 2)}</pre>
                  </div>
                </div>
              )}

              {testResult.records && testResult.records.length > 0 && (
                <div>
                  <h3 className="font-semibold mb-2">Sample Records:</h3>
                  <div className="bg-gray-50 p-4 rounded text-sm max-h-96 overflow-auto">
                    <pre>{JSON.stringify(testResult.records.slice(0, 3), null, 2)}</pre>
                  </div>
                </div>
              )}

              {testResult.records && testResult.records.length === 0 && testResult.tableExists && (
                <div className="bg-yellow-50 border border-yellow-200 rounded p-4">
                  <strong>Warning:</strong> Table exists but contains no data.
                  <div className="mt-2 text-sm">
                    You need to add some internship applications to see them in the dashboard.
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div>Click "Run Test" to check the database</div>
          )}
        </CardContent>
      </Card>

      {testResult && !testResult.tableExists && (
        <Card>
          <CardHeader>
            <CardTitle>Setup Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p>The internship_applications table doesn't exist. You need to create it first.</p>
              <div className="bg-gray-50 p-4 rounded">
                <h4 className="font-semibold mb-2">Run this SQL command:</h4>
                <pre className="text-sm overflow-auto">
{`CREATE TABLE internship_applications (
    application_id INT PRIMARY KEY AUTO_INCREMENT,
    applicant_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50) NOT NULL,
    education_level ENUM('high-school', 'diploma', 'bachelors', 'masters', 'phd', 'other') NOT NULL,
    internship_type ENUM('web-development', 'mobile-development', 'data-science', 'ui-ux-design', 'digital-marketing', 'business-analysis', 'other') NOT NULL,
    preferred_duration ENUM('3-months', '6-months', '12-months', 'flexible') NOT NULL,
    availability ENUM('full-time', 'part-time', 'flexible') NOT NULL,
    remote_preference ENUM('remote', 'on-site', 'hybrid', 'no-preference') NOT NULL,
    status ENUM('pending', 'reviewed', 'interview-scheduled', 'interviewed', 'accepted', 'rejected', 'withdrawn') DEFAULT 'pending',
    application_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);`}
                </pre>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
