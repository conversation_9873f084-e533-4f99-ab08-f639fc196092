import { NextResponse } from 'next/server';
import pool from '@/lib/db';
import { Curriculum } from '@/lib/types';
import { ResultSetHeader, RowDataPacket } from 'mysql2';

export async function GET(request: Request) {
  try {
    const id = request.url.split('/').pop();

    if (!id) {
      return NextResponse.json(
        { error: 'Missing curriculum ID' },
        { status: 400 }
      );
    }

    const [rows] = await pool.query<(Curriculum & RowDataPacket)[]>(
      'SELECT * FROM curriculum WHERE curriculum_id = ?',
      [id]
    );
    
    if (!rows[0]) {
      return NextResponse.json(
        { error: 'Curriculum not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(rows[0]);
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request) {
  try {
    const id = request.url.split('/').pop();

    if (!id) {
      return NextResponse.json(
        { error: 'Missing curriculum ID' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { course_id, week_number, topic, content, learning_objectives } = body;

    const [result] = await pool.query<ResultSetHeader>(
      `UPDATE curriculum 
       SET course_id = ?, week_number = ?, topic = ?, content = ?, learning_objectives = ?
       WHERE curriculum_id = ?`,
      [course_id, week_number, topic, content, learning_objectives, id]
    );

    if (result.affectedRows === 0) {
      return NextResponse.json(
        { error: 'Curriculum not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: Request) {
  try {
    const id = request.url.split('/').pop();

    if (!id) {
      return NextResponse.json(
        { error: 'Missing curriculum ID' },
        { status: 400 }
      );
    }

    const [result] = await pool.query<ResultSetHeader>(
      'DELETE FROM curriculum WHERE curriculum_id = ?',
      [id]
    );

    if (result.affectedRows === 0) {
      return NextResponse.json(
        { error: 'Curriculum not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
} 