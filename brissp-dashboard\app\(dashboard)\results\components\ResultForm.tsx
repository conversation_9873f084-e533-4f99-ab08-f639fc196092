'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Course, Enrollment } from './../types';

interface ResultFormProps {
  courses: Course[];
  selectedCourse: string;
  onCourseSelect: (courseId: string) => void;
}

export function ResultForm({ courses, selectedCourse, onCourseSelect }: ResultFormProps) {
  const [enrolledStudents, setEnrolledStudents] = useState<Enrollment[]>([]);
  const [formData, setFormData] = useState({
    user_id: '',
    enrollment_id: '',
    assessment_type: '',
    assessment_title: '',
    score: '',
    max_score: '100',
    result_date: '',
    comments: '',
    is_passed: false
  });

  useEffect(() => {
    if (selectedCourse) {
      fetchEnrolledStudents();
    }
  }, [selectedCourse]);

  const fetchEnrolledStudents = async () => {
    try {
      const response = await fetch(`/api/enrollments?courseId=${selectedCourse}&status=active`);
      const data = await response.json();
      setEnrolledStudents(data);
    } catch {
      toast.error('Failed to fetch enrolled students');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const response = await fetch('/api/results', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          course_id: selectedCourse,
          score: parseFloat(formData.score),
          max_score: parseFloat(formData.max_score),
        }),
      });

      if (!response.ok) throw new Error('Failed to submit result');
      
      toast.success('Result added successfully');
      setFormData({
        user_id: '',
        enrollment_id: '',
        assessment_type: '',
        assessment_title: '',
        score: '',
        max_score: '100',
        result_date: '',
        comments: '',
        is_passed: false
      });
    } catch {
      toast.error('Failed to submit result');
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Add New Result</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid gap-4">
            <div className="grid gap-2">
              <Label htmlFor="course">Course</Label>
              <Select value={selectedCourse} onValueChange={onCourseSelect}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a course" />
                </SelectTrigger>
                <SelectContent>
                  {courses.map((course) => (
                    <SelectItem key={course.course_id} value={course.course_id.toString()}>
                      {course.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="student">Student</Label>
              <Select 
                value={formData.enrollment_id} 
                onValueChange={(value) => {
                  const student = enrolledStudents.find(s => s.enrollment_id.toString() === value);
                  setFormData({
                    ...formData,
                    enrollment_id: value,
                    user_id: student ? student.user_id.toString() : ''
                  });
                }}
                disabled={!selectedCourse}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a student" />
                </SelectTrigger>
                <SelectContent>
                  {enrolledStudents.map((student) => (
                    <SelectItem key={student.enrollment_id} value={student.enrollment_id.toString()}>
                      {`${student.first_name} ${student.last_name}`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="assessment-type">Assessment Type</Label>
              <Select 
                value={formData.assessment_type} 
                onValueChange={(value) => setFormData({ ...formData, assessment_type: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select assessment type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="quiz">Quiz</SelectItem>
                  <SelectItem value="exam">Exam</SelectItem>
                  <SelectItem value="assignment">Assignment</SelectItem>
                  <SelectItem value="project">Project</SelectItem>
                  <SelectItem value="final">Final</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="assessment-title">Assessment Title</Label>
              <Input 
                id="assessment-title"
                placeholder="Enter assessment title" 
                value={formData.assessment_title}
                onChange={(e) => setFormData({ ...formData, assessment_title: e.target.value })}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="score">Score</Label>
                <Input 
                  id="score"
                  type="number"
                  placeholder="Enter score" 
                  value={formData.score}
                  onChange={(e) => setFormData({ ...formData, score: e.target.value })}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="max-score">Max Score</Label>
                <Input 
                  id="max-score"
                  type="number"
                  placeholder="Enter max score" 
                  value={formData.max_score}
                  onChange={(e) => setFormData({ ...formData, max_score: e.target.value })}
                />
              </div>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="result-date">Result Date</Label>
              <Input 
                id="result-date"
                type="date"
                value={formData.result_date}
                onChange={(e) => setFormData({ ...formData, result_date: e.target.value })}
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="comments">Comments</Label>
              <Textarea 
                id="comments"
                placeholder="Enter comments (optional)"
                value={formData.comments}
                onChange={(e) => setFormData({ ...formData, comments: e.target.value })}
                rows={3}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox 
                id="is-passed"
                checked={formData.is_passed}
                onCheckedChange={(checked) => 
                  setFormData({ ...formData, is_passed: checked as boolean })
                }
              />
              <Label htmlFor="is-passed" className="font-normal">
                Mark as passed
              </Label>
            </div>

            <Button type="submit" className="w-full">Submit Result</Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}