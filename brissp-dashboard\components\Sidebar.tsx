"use client";

import Link from "next/link"
import { Bar<PERSON>hart2, BellDot, GraduationCap, LogOut, ClipboardList, Award, Download, BookOpen, Layers, Key, Presentation, Briefcase, FileText } from "lucide-react"
import { useRouter } from "next/navigation"
import { toast } from "sonner"

const Sidebar = () => {
  const router = useRouter();

  const handleLogout = async () => {
    try {
      const response = await fetch("/api/admin/auth/logout", {
        method: "POST",
        credentials: "include",
      });

      if (response.ok) {
        toast.success("Logged out successfully");
        router.push("/");
      } else {
        toast.error("Failed to log out");
      }
    } catch (error) {
      console.error("Logout error:", error);
      toast.error("An error occurred during logout");
    }
  };

  return (
   
    <div className="w-64 border-r bg-white h-screen flex flex-col fixed">
      <div className="p-4 border-b">
        <Link href="/panel" className="flex items-center space-x-2">
          <span className="font-semibold text-lg">Brissp Dash</span>
        </Link>
      </div>
      <nav className="p-4 space-y-2 flex-grow">
        <Link
          href="/panel"
          className="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 text-gray-900"
        >
          <BarChart2 className="w-5 h-5" />
          <span>Dashboard</span>
        </Link>
        <Link
          href="/applications"
          className="flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-100"
        >
          <ClipboardList className="w-5 h-5" />
          <span>Course Applications</span>
        </Link>
        <Link
          href="/pitch-deck-applications"
          className="flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-100"
        >
          <Presentation className="w-5 h-5" />
          <span>Pitch Deck Applications</span>
        </Link>
        <Link
          href="/internship-applications"
          className="flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-100"
        >
          <Briefcase className="w-5 h-5" />
          <span>Internship Applications</span>
        </Link>
        <Link
          href="/fyp-applications"
          className="flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-100"
        >
          <FileText className="w-5 h-5" />
          <span>FYP Applications</span>
        </Link>
        <Link
          href="/debug-internships"
          className="flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-100"
        >
          <FileText className="w-5 h-5" />
          <span>Debug Internships</span>
        </Link>
        <Link
          href="/results"
          className="flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-100"
        >
          <Award className="w-5 h-5" />
          <span>Results</span>
        </Link>
        <Link
          href="/downloadables"
          className="flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-100"
        >
          <Download className="w-5 h-5" />
          <span>Resources</span>
        </Link>
        <Link
          href="/courses"
          className="flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-100"
        >
          <BookOpen className="w-5 h-5" />
          <span>Courses</span>
        </Link>
        <Link
          href="/curriculum"
          className="flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-100"
        >
          <Layers className="w-5 h-5" />
          <span>Curriculum</span>
        </Link>
        <Link
          href="/notice-board"
          className="flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-100"
        >
          <BellDot className="w-5 h-5" />
          <span>Notice Board</span>
        </Link>
        <Link
          href="/graduates"
          className="flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-100"
        >
          <GraduationCap className="w-5 h-5" />
          <span>Graduates</span>
        </Link>
        <Link
          href="/password-management"
          className="flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-100"
        >
          <Key className="w-5 h-5" />
          <span>Password Management</span>
        </Link>
      </nav>
      <div className="p-4 border-t mt-auto">
        <button
          onClick={handleLogout}
          className="flex items-center space-x-2 px-3 py-2 rounded-lg text-red-600 hover:bg-red-50 w-full"
        >
          <LogOut className="w-5 " />
          <span>Logout</span>
        </button>
      </div>
    </div>

  )
}

export default Sidebar