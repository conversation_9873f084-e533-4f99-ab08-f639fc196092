/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextResponse } from 'next/server';
import pool from '@/lib/db';

export async function GET(request: Request) {
  try {
    const id = request.url.split('/').pop();

    if (!id) {
      return NextResponse.json(
        { error: 'Missing course ID' },
        { status: 400 }
      );
    }

    const [courses] = await pool.query(
      `SELECT * FROM courses WHERE course_id = ?`,
      [id]
    );
    
    const course = (courses as any[])[0];
    
    if (!course) {
      return NextResponse.json(
        { error: 'Course not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(course);
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request) {
  try {
    const id = request.url.split('/').pop();

    if (!id) {
      return NextResponse.json(
        { error: 'Missing course ID' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const {
      title, description, duration_months, price, department,
      category, image_url, program_type, num_lectures,
      skill_level, languages, class_days, course_code
    } = body;

    const [result] = await pool.query(
      `UPDATE courses SET
        title = ?, description = ?, duration_months = ?, 
        price = ?, department = ?, category = ?, 
        image_url = ?, program_type = ?, num_lectures = ?,
        skill_level = ?, languages = ?, class_days = ?,
        course_code = ?
      WHERE course_id = ?`,
      [title, description, duration_months, price, department,
       category, image_url, program_type, num_lectures,
       skill_level, languages, class_days, course_code,
       id]
    );

    if ((result as any).affectedRows === 0) {
      return NextResponse.json(
        { error: 'Course not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Course updated successfully' });
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: Request) {
  try {
    const id = request.url.split('/').pop();

    if (!id) {
      return NextResponse.json(
        { error: 'Missing course ID' },
        { status: 400 }
      );
    }

    const [result] = await pool.query(
      'DELETE FROM courses WHERE course_id = ?',
      [id]
    );

    if ((result as any).affectedRows === 0) {
      return NextResponse.json(
        { error: 'Course not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { message: 'Course deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
