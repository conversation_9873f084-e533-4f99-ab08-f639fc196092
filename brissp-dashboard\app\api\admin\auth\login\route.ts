/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextRequest, NextResponse } from 'next/server';
import pool from '@/lib/db';
import { generateToken } from '@/lib/auth';

export async function POST(req: NextRequest) {
  try {
    const { email, password } = await req.json();

    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Get user from database with direct password comparison
    const [rows] = await pool.query(
      'SELECT * FROM admin_users WHERE email = ? AND password = ?',
      [email, password]
    );

    const users = rows as any[];
    if (users.length === 0) {
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      );
    }

    const user = users[0];

    // Update last login
    await pool.query(
      'UPDATE admin_users SET last_login = CURRENT_TIMESTAMP WHERE admin_id = ?',
      [user.admin_id]
    );

    // Generate token
    const token = generateToken({
      admin_id: user.admin_id,
      email: user.email,
      full_name: user.full_name,
      is_super_admin: user.is_super_admin
    });

    // Create response
    const response = NextResponse.json({
      message: 'Login successful',
      user: {
        admin_id: user.admin_id,
        email: user.email,
        full_name: user.full_name,
        is_super_admin: user.is_super_admin
      }
    });

    // Set cookie directly on the response
    response.cookies.set({
      name: 'admin_token',
      value: token,
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 60 * 60 * 24, // 1 day
      path: '/',
      sameSite: 'lax',
    });

    return response;
  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { error: 'An error occurred during login' },
      { status: 500 }
    );
  }
}